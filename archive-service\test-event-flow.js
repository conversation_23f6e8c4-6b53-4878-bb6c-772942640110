/**
 * Test Event Flow
 * Simple test to verify event-driven archiving is working
 */

const amqp = require('amqplib');
const { v4: uuidv4 } = require('uuid');

// Configuration
const RABBITMQ_URL = process.env.RABBITMQ_URL || 'amqp://localhost:5672';
const EVENTS_EXCHANGE = 'atma_events_exchange';

/**
 * Publish a test analysis completed event
 */
async function publishTestEvent() {
  let connection;
  let channel;

  try {
    console.log('Connecting to RabbitMQ...');
    connection = await amqp.connect(RABBITMQ_URL);
    channel = await connection.createChannel();

    // Assert exchange
    await channel.assertExchange(EVENTS_EXCHANGE, 'topic', { durable: true });

    // Create test event data
    const testEvent = {
      eventType: 'analysis.completed',
      timestamp: new Date().toISOString(),
      jobId: `test-job-${Date.now()}`,
      userId: uuidv4(),
      userEmail: '<EMAIL>',
      resultId: uuidv4(),
      metadata: {
        assessmentName: 'AI-Driven Talent Mapping',
        processingTime: 5000,
        retryCount: 0
      }
    };

    console.log('Publishing test event:', JSON.stringify(testEvent, null, 2));

    // Publish event
    const published = channel.publish(
      EVENTS_EXCHANGE,
      'analysis.completed',
      Buffer.from(JSON.stringify(testEvent)),
      {
        persistent: true,
        timestamp: Date.now(),
        messageId: `${testEvent.jobId}-${testEvent.eventType}-${Date.now()}`
      }
    );

    if (published) {
      console.log('✅ Test event published successfully!');
      console.log('Check the archive-service logs to see if the event was consumed.');
    } else {
      console.log('❌ Failed to publish test event');
    }

  } catch (error) {
    console.error('❌ Error publishing test event:', error.message);
  } finally {
    if (channel) {
      await channel.close();
    }
    if (connection) {
      await connection.close();
    }
  }
}

/**
 * Publish a test analysis failed event
 */
async function publishTestFailedEvent() {
  let connection;
  let channel;

  try {
    console.log('Connecting to RabbitMQ for failed event test...');
    connection = await amqp.connect(RABBITMQ_URL);
    channel = await connection.createChannel();

    // Assert exchange
    await channel.assertExchange(EVENTS_EXCHANGE, 'topic', { durable: true });

    // Create test failed event data
    const testEvent = {
      eventType: 'analysis.failed',
      timestamp: new Date().toISOString(),
      jobId: `test-failed-job-${Date.now()}`,
      userId: uuidv4(),
      userEmail: '<EMAIL>',
      errorMessage: 'Test error for event-driven archiving',
      metadata: {
        assessmentName: 'AI-Driven Talent Mapping',
        processingTime: 2000,
        retryCount: 1,
        errorType: 'test_error'
      }
    };

    console.log('Publishing test failed event:', JSON.stringify(testEvent, null, 2));

    // Publish event
    const published = channel.publish(
      EVENTS_EXCHANGE,
      'analysis.failed',
      Buffer.from(JSON.stringify(testEvent)),
      {
        persistent: true,
        timestamp: Date.now(),
        messageId: `${testEvent.jobId}-${testEvent.eventType}-${Date.now()}`
      }
    );

    if (published) {
      console.log('✅ Test failed event published successfully!');
      console.log('Check the archive-service logs to see if the event was consumed.');
    } else {
      console.log('❌ Failed to publish test failed event');
    }

  } catch (error) {
    console.error('❌ Error publishing test failed event:', error.message);
  } finally {
    if (channel) {
      await channel.close();
    }
    if (connection) {
      await connection.close();
    }
  }
}

// Main execution
async function main() {
  console.log('🧪 Testing Event-Driven Archiving Flow');
  console.log('=====================================');
  
  // Test completed event
  await publishTestEvent();
  
  // Wait a bit
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test failed event
  await publishTestFailedEvent();
  
  console.log('\n✅ Test completed! Check the archive-service logs for event consumption.');
  console.log('You should see log entries showing the events were processed.');
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  publishTestEvent,
  publishTestFailedEvent
};
