/**
 * Master Test Runner
 * <PERSON><PERSON><PERSON>an semua test suites untuk validasi implementasi idempotency dan retry
 */

const { spawn } = require('child_process');
const path = require('path');

// Test configuration
const testConfig = {
  timeout: 120000, // 2 minutes per test suite
  retryAttempts: 2,
  services: [
    { name: 'API Gateway', port: 3000 },
    { name: 'Auth Service', port: 3001 },
    { name: 'Assessment Service', port: 3002 },
    { name: 'Archive Service', port: 3003 },
    { name: 'Analysis Worker', port: 3004 },
    { name: 'Notification Service', port: 3005 }
  ]
};

// Test suites to run
const testSuites = [
  {
    name: 'Unit Tests',
    script: 'test-unit-idempotency.js',
    description: 'Tests individual components without external dependencies',
    critical: true
  },
  {
    name: 'Basic Authentication Flow',
    script: 'test-runner.js',
    description: 'Tests basic authentication and user management',
    critical: true
  },
  {
    name: 'Comprehensive Idempotency Tests',
    script: 'test-idempotency-comprehensive.js',
    description: 'Tests all idempotency and retry mechanisms',
    critical: true
  },
  {
    name: 'WebSocket Flow Test',
    script: 'test-runner.js',
    args: ['--websocket'],
    description: 'Tests WebSocket notifications and deduplication',
    critical: false
  }
];

// Results tracking
const results = {
  total: 0,
  passed: 0,
  failed: 0,
  skipped: 0,
  details: []
};

/**
 * Utility functions
 */
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📋',
    success: '✅',
    error: '❌',
    warning: '⚠️',
    debug: '🔍'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Check if services are running
 */
async function checkServicesHealth() {
  log('Checking services health...', 'info');
  
  const axios = require('axios');
  const healthChecks = [];
  
  for (const service of testConfig.services) {
    healthChecks.push(
      axios.get(`http://localhost:${service.port}/health`, { timeout: 5000 })
        .then(() => ({ name: service.name, status: 'healthy' }))
        .catch(() => ({ name: service.name, status: 'unhealthy' }))
    );
  }
  
  try {
    const healthResults = await Promise.all(healthChecks);
    const healthyServices = healthResults.filter(r => r.status === 'healthy');
    const unhealthyServices = healthResults.filter(r => r.status === 'unhealthy');
    
    log(`Services health check completed: ${healthyServices.length}/${healthResults.length} healthy`, 'info');
    
    if (unhealthyServices.length > 0) {
      log(`Unhealthy services: ${unhealthyServices.map(s => s.name).join(', ')}`, 'warning');
    }
    
    return {
      healthy: healthyServices.length,
      total: healthResults.length,
      allHealthy: unhealthyServices.length === 0,
      details: healthResults
    };
  } catch (error) {
    log(`Health check failed: ${error.message}`, 'error');
    return { healthy: 0, total: testConfig.services.length, allHealthy: false };
  }
}

/**
 * Run a single test suite
 */
function runTestSuite(testSuite) {
  return new Promise((resolve) => {
    log(`Starting ${testSuite.name}...`, 'info');
    log(`Description: ${testSuite.description}`, 'debug');
    
    const args = testSuite.args || [];
    const child = spawn('node', [testSuite.script, ...args], {
      stdio: 'pipe',
      cwd: process.cwd()
    });
    
    let output = '';
    let errorOutput = '';
    
    child.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    child.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    const timeout = setTimeout(() => {
      child.kill('SIGTERM');
      resolve({
        name: testSuite.name,
        success: false,
        error: 'Test timeout',
        output: output,
        duration: testConfig.timeout
      });
    }, testConfig.timeout);
    
    const startTime = Date.now();
    
    child.on('close', (code) => {
      clearTimeout(timeout);
      const duration = Date.now() - startTime;
      
      const success = code === 0;
      const result = {
        name: testSuite.name,
        success,
        code,
        output,
        errorOutput,
        duration,
        critical: testSuite.critical
      };
      
      if (success) {
        log(`${testSuite.name} completed successfully (${duration}ms)`, 'success');
      } else {
        log(`${testSuite.name} failed with code ${code} (${duration}ms)`, 'error');
        if (errorOutput) {
          log(`Error output: ${errorOutput.slice(0, 200)}...`, 'debug');
        }
      }
      
      resolve(result);
    });
    
    child.on('error', (error) => {
      clearTimeout(timeout);
      const duration = Date.now() - startTime;
      
      log(`${testSuite.name} failed to start: ${error.message}`, 'error');
      resolve({
        name: testSuite.name,
        success: false,
        error: error.message,
        duration,
        critical: testSuite.critical
      });
    });
  });
}

/**
 * Generate test report
 */
function generateReport() {
  console.log('\n' + '='.repeat(80));
  console.log('📊 COMPREHENSIVE TEST REPORT');
  console.log('='.repeat(80));
  
  console.log(`\n📈 Summary:`);
  console.log(`   Total Test Suites: ${results.total}`);
  console.log(`   Passed: ${results.passed} ✅`);
  console.log(`   Failed: ${results.failed} ❌`);
  console.log(`   Skipped: ${results.skipped} ⏭️`);
  console.log(`   Success Rate: ${((results.passed / results.total) * 100).toFixed(1)}%`);
  
  console.log(`\n📋 Detailed Results:`);
  results.details.forEach((result, index) => {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    const duration = `${result.duration}ms`;
    const critical = result.critical ? '🔴 CRITICAL' : '🟡 NON-CRITICAL';
    
    console.log(`   ${index + 1}. ${status} ${result.name} (${duration}) ${critical}`);
    
    if (!result.success && result.error) {
      console.log(`      Error: ${result.error}`);
    }
  });
  
  // Critical failures
  const criticalFailures = results.details.filter(r => !r.success && r.critical);
  if (criticalFailures.length > 0) {
    console.log(`\n🚨 Critical Failures:`);
    criticalFailures.forEach(failure => {
      console.log(`   - ${failure.name}: ${failure.error || 'Test failed'}`);
    });
  }
  
  // Recommendations
  console.log(`\n💡 Recommendations:`);
  if (results.failed === 0) {
    console.log('   ✅ All tests passed! Implementation is ready for production.');
    console.log('   ✅ Idempotency and retry mechanisms are working correctly.');
    console.log('   ✅ Consider running load tests for production readiness.');
  } else if (criticalFailures.length === 0) {
    console.log('   ⚠️ Some non-critical tests failed, but core functionality works.');
    console.log('   ⚠️ Review failed tests and consider fixes for completeness.');
    console.log('   ✅ Safe to proceed with deployment with monitoring.');
  } else {
    console.log('   ❌ Critical tests failed - DO NOT DEPLOY to production.');
    console.log('   ❌ Fix critical issues before proceeding.');
    console.log('   ❌ Ensure all services are running and properly configured.');
  }
  
  // Overall status
  const overallStatus = criticalFailures.length === 0 ? 'READY FOR DEPLOYMENT' : 'NOT READY FOR DEPLOYMENT';
  console.log(`\n🎯 Overall Status: ${overallStatus}`);
  
  return criticalFailures.length === 0;
}

/**
 * Main test execution
 */
async function runAllTests() {
  console.log('🚀 Starting Comprehensive Test Suite for ATMA Backend');
  console.log('🎯 Testing Idempotency and Retry Mechanisms Implementation');
  console.log('='.repeat(80));
  
  // Pre-flight checks
  log('Performing pre-flight checks...', 'info');
  
  // Check if required files exist
  const fs = require('fs');
  const missingFiles = testSuites
    .map(suite => suite.script)
    .filter(script => !fs.existsSync(script));
  
  if (missingFiles.length > 0) {
    log(`Missing test files: ${missingFiles.join(', ')}`, 'error');
    return false;
  }
  
  // Check services health
  const healthCheck = await checkServicesHealth();
  if (!healthCheck.allHealthy) {
    log(`Warning: Not all services are healthy (${healthCheck.healthy}/${healthCheck.total})`, 'warning');
    log('Some tests may fail due to service unavailability', 'warning');
    
    // Wait a bit for services to potentially start up
    log('Waiting 10 seconds for services to stabilize...', 'info');
    await sleep(10000);
  }
  
  // Run test suites
  log('Starting test execution...', 'info');
  
  for (const testSuite of testSuites) {
    results.total++;
    
    const result = await runTestSuite(testSuite);
    results.details.push(result);
    
    if (result.success) {
      results.passed++;
    } else {
      results.failed++;
      
      // If critical test fails, consider stopping
      if (result.critical && testSuite.name === 'Unit Tests') {
        log('Critical unit tests failed - stopping execution', 'error');
        break;
      }
    }
    
    // Small delay between tests
    await sleep(2000);
  }
  
  // Generate final report
  const success = generateReport();
  
  return success;
}

// Execute tests
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      log(`Test execution failed: ${error.message}`, 'error');
      console.error(error);
      process.exit(1);
    });
}

module.exports = { runAllTests, checkServicesHealth };
