/**
 * Event Consumer Service for Archive Service
 * Consumes analysis events from RabbitMQ for event-driven archiving
 */

const rabbitmq = require('../config/rabbitmq');
const AnalysisJob = require('../models/AnalysisJob');
const logger = require('../utils/logger');

// Consumer state
let isConsuming = false;
let channel = null;

/**
 * Initialize event consumer
 */
const initialize = async () => {
  try {
    // Initialize RabbitMQ connection
    channel = await rabbitmq.initialize();
    
    logger.info('Archive Service event consumer initialized');
  } catch (error) {
    logger.error('Failed to initialize Archive Service event consumer', { error: error.message });
    throw error;
  }
};

/**
 * Start consuming events from the archive queue
 */
const startConsuming = async () => {
  try {
    if (isConsuming) {
      logger.warn('Archive Service event consumer is already consuming messages');
      return;
    }

    if (!channel) {
      throw new Error('Event consumer not initialized');
    }

    isConsuming = true;

    logger.info('Starting Archive Service event consumption', {
      queue: rabbitmq.config.archiveQueue,
      prefetch: rabbitmq.config.consumerOptions.prefetch
    });

    // Start consuming events
    await channel.consume(rabbitmq.config.archiveQueue, async (message) => {
      if (message) {
        try {
          const eventData = JSON.parse(message.content.toString());
          
          logger.debug('Received archive event', {
            eventType: eventData.eventType,
            jobId: eventData.jobId,
            userId: eventData.userId
          });

          // Process the event based on type
          await processEvent(eventData);

          // Acknowledge the message
          channel.ack(message);

          logger.debug('Archive event processed successfully', {
            eventType: eventData.eventType,
            jobId: eventData.jobId,
            userId: eventData.userId
          });

        } catch (error) {
          logger.error('Failed to process archive event', {
            error: error.message,
            messageContent: message.content.toString(),
            stack: error.stack
          });

          // Reject message and send to dead letter queue
          channel.nack(message, false, false);
        }
      }
    }, {
      noAck: false
    });

    logger.info('Archive Service event consumer started successfully');

  } catch (error) {
    logger.error('Failed to start Archive Service event consumer', { error: error.message });
    isConsuming = false;
    throw error;
  }
};

/**
 * Stop consuming events
 */
const stopConsuming = async () => {
  try {
    if (!isConsuming) {
      logger.warn('Archive Service event consumer is not consuming');
      return;
    }

    isConsuming = false;

    if (channel) {
      await channel.cancel(rabbitmq.config.archiveQueue);
    }

    logger.info('Archive Service event consumer stopped');
  } catch (error) {
    logger.error('Failed to stop Archive Service event consumer', { error: error.message });
    throw error;
  }
};

/**
 * Process individual event based on type
 * @param {Object} eventData - Event data from RabbitMQ
 */
const processEvent = async (eventData) => {
  const { eventType, userId, jobId } = eventData;

  switch (eventType) {
    case 'analysis.completed':
      await handleAnalysisCompleted(eventData);
      break;
    
    case 'analysis.failed':
      await handleAnalysisFailed(eventData);
      break;
    
    case 'analysis.started':
      await handleAnalysisStarted(eventData);
      break;
    
    default:
      logger.warn('Unknown archive event type received', {
        eventType,
        jobId,
        userId
      });
  }
};

/**
 * Handle analysis completed events
 * Updates job status and creates audit trail
 * @param {Object} eventData - Event data
 */
const handleAnalysisCompleted = async (eventData) => {
  const { userId, jobId, resultId, metadata } = eventData;

  try {
    // Find the analysis job
    const job = await AnalysisJob.findByJobId(jobId);
    
    if (!job) {
      logger.warn('Analysis job not found for completed event', {
        jobId,
        userId,
        resultId
      });
      return;
    }

    // Update job status to completed
    await job.update({
      status: 'completed',
      result_id: resultId,
      completed_at: new Date(),
      updated_at: new Date()
    });

    logger.info('Analysis job status updated to completed via event', {
      userId,
      jobId,
      resultId,
      processingTime: metadata?.processingTime,
      assessmentName: metadata?.assessmentName
    });

    // Create audit trail entry
    await createAuditTrail({
      eventType: 'analysis.completed',
      jobId,
      userId,
      resultId,
      metadata: {
        processingTime: metadata?.processingTime,
        assessmentName: metadata?.assessmentName,
        source: 'event-driven'
      }
    });

  } catch (error) {
    logger.error('Failed to handle analysis completed event', {
      userId,
      jobId,
      resultId,
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
};

/**
 * Handle analysis failed events
 * Updates job status and logs failure details
 * @param {Object} eventData - Event data
 */
const handleAnalysisFailed = async (eventData) => {
  const { userId, jobId, errorMessage, metadata } = eventData;

  try {
    // Find the analysis job
    const job = await AnalysisJob.findByJobId(jobId);
    
    if (!job) {
      logger.warn('Analysis job not found for failed event', {
        jobId,
        userId,
        errorMessage
      });
      return;
    }

    // Update job status to failed
    await job.update({
      status: 'failed',
      error_message: errorMessage,
      completed_at: new Date(),
      updated_at: new Date()
    });

    logger.info('Analysis job status updated to failed via event', {
      userId,
      jobId,
      errorMessage,
      errorType: metadata?.errorType
    });

    // Create audit trail entry
    await createAuditTrail({
      eventType: 'analysis.failed',
      jobId,
      userId,
      errorMessage,
      metadata: {
        errorType: metadata?.errorType,
        retryCount: metadata?.retryCount,
        source: 'event-driven'
      }
    });

  } catch (error) {
    logger.error('Failed to handle analysis failed event', {
      userId,
      jobId,
      errorMessage,
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
};

/**
 * Handle analysis started events (optional for future use)
 * Updates job status to processing
 * @param {Object} eventData - Event data
 */
const handleAnalysisStarted = async (eventData) => {
  const { userId, jobId, metadata } = eventData;

  try {
    // Find the analysis job
    const job = await AnalysisJob.findByJobId(jobId);
    
    if (!job) {
      logger.warn('Analysis job not found for started event', {
        jobId,
        userId
      });
      return;
    }

    // Update job status to processing
    await job.update({
      status: 'processing',
      processing_started_at: new Date(),
      updated_at: new Date()
    });

    logger.info('Analysis job status updated to processing via event', {
      userId,
      jobId,
      assessmentName: metadata?.assessmentName
    });

    // Create audit trail entry
    await createAuditTrail({
      eventType: 'analysis.started',
      jobId,
      userId,
      metadata: {
        assessmentName: metadata?.assessmentName,
        estimatedProcessingTime: metadata?.estimatedProcessingTime,
        source: 'event-driven'
      }
    });

  } catch (error) {
    logger.error('Failed to handle analysis started event', {
      userId,
      jobId,
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
};

/**
 * Create audit trail entry for archiving activities
 * @param {Object} auditData - Audit data
 */
const createAuditTrail = async (auditData) => {
  try {
    // Log audit trail (could be extended to save to database)
    logger.info('Archive audit trail', {
      timestamp: new Date().toISOString(),
      ...auditData
    });

    // TODO: Could be extended to save audit trails to a dedicated audit table
    // await AuditTrail.create({
    //   event_type: auditData.eventType,
    //   job_id: auditData.jobId,
    //   user_id: auditData.userId,
    //   result_id: auditData.resultId,
    //   metadata: auditData.metadata,
    //   created_at: new Date()
    // });

  } catch (error) {
    logger.error('Failed to create audit trail', {
      error: error.message,
      auditData
    });
    // Don't throw error to avoid breaking the main event processing
  }
};

/**
 * Get consumer status and statistics
 * @returns {Object} - Consumer status
 */
const getStatus = () => {
  return {
    isConsuming,
    isConnected: !!channel,
    config: rabbitmq.getConfig(),
    timestamp: new Date().toISOString()
  };
};

/**
 * Health check for the event consumer
 * @returns {Promise<boolean>} - Health status
 */
const checkHealth = async () => {
  try {
    if (!channel) {
      return false;
    }

    // Check RabbitMQ connection health
    const isHealthy = await rabbitmq.checkHealth();
    
    return isHealthy && isConsuming;
  } catch (error) {
    logger.error('Archive Service event consumer health check failed', { error: error.message });
    return false;
  }
};

module.exports = {
  initialize,
  startConsuming,
  stopConsuming,
  getStatus,
  checkHealth
};
