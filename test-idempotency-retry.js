/**
 * Test Idempotency and Retry Mechanisms
 * Tests the implementation of idempotency and retry features
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// Test configuration
const config = {
  apiGateway: 'http://localhost:3000',
  testTimeout: 30000
};

// Test user data
const testUser = {
  email: `testuser_${Date.now()}_${Math.floor(Math.random() * 10000)}@example.com`,
  password: 'TestPassword123!',
  username: `testuser${Date.now()}${Math.floor(Math.random() * 10000)}`
};

// Sample assessment data
const assessmentData = {
  riasec: {
    realistic: 4,
    investigative: 5,
    artistic: 3,
    social: 4,
    enterprising: 2,
    conventional: 3
  },
  ocean: {
    openness: 4,
    conscientiousness: 5,
    extraversion: 3,
    agreeableness: 4,
    neuroticism: 2
  },
  viaIs: {
    creativity: 4,
    curiosity: 5,
    judgment: 4,
    love_of_learning: 5,
    perspective: 4,
    bravery: 3,
    perseverance: 4,
    honesty: 5,
    zest: 3,
    love: 4,
    kindness: 4,
    social_intelligence: 3,
    teamwork: 4,
    fairness: 5,
    leadership: 3,
    forgiveness: 4,
    humility: 4,
    prudence: 5,
    self_regulation: 4,
    appreciation_of_beauty: 3,
    gratitude: 4,
    hope: 4,
    humor: 3,
    spirituality: 3
  }
};

let authToken = null;
let userId = null;

/**
 * Register and authenticate test user
 */
async function setupTestUser() {
  console.log('\n🔧 Setting up test user...');
  
  try {
    // Register user
    const registerResponse = await axios.post(`${config.apiGateway}/auth/register`, {
      email: testUser.email,
      password: testUser.password,
      username: testUser.username
    });
    
    if (registerResponse.data.success) {
      authToken = registerResponse.data.data.token;
      userId = registerResponse.data.data.user.id;
      console.log('✅ Test user registered successfully');
      console.log(`   User ID: ${userId}`);
      return true;
    } else {
      console.log('❌ Failed to register test user');
      return false;
    }
  } catch (error) {
    console.log('❌ Error setting up test user:', error.response?.data?.error || error.message);
    return false;
  }
}

/**
 * Test 1: Assessment Submission Idempotency
 */
async function testAssessmentIdempotency() {
  console.log('\n🧪 Test 1: Assessment Submission Idempotency');
  console.log('='.repeat(50));
  
  try {
    // Generate a unique assessment ID
    const assessmentId = uuidv4();
    console.log(`Generated assessment ID: ${assessmentId}`);
    
    const assessmentPayload = {
      ...assessmentData,
      assessment_id: assessmentId,
      assessmentName: 'AI-Driven Talent Mapping'
    };
    
    // First submission
    console.log('\n📤 First submission...');
    const firstResponse = await axios.post(
      `${config.apiGateway}/assessments/submit`,
      assessmentPayload,
      {
        headers: { Authorization: `Bearer ${authToken}` }
      }
    );
    
    console.log(`✅ First submission: ${firstResponse.status} - ${firstResponse.data.message}`);
    console.log(`   Job ID: ${firstResponse.data.data.jobId}`);
    console.log(`   Assessment ID: ${firstResponse.data.data.assessmentId}`);
    
    const firstJobId = firstResponse.data.data.jobId;
    
    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Second submission with same assessment_id (should be idempotent)
    console.log('\n📤 Second submission (same assessment_id)...');
    const secondResponse = await axios.post(
      `${config.apiGateway}/assessments/submit`,
      assessmentPayload,
      {
        headers: { Authorization: `Bearer ${authToken}` }
      }
    );
    
    console.log(`✅ Second submission: ${secondResponse.status} - ${secondResponse.data.message}`);
    
    // Check if idempotency worked
    if (secondResponse.data.data.idempotent) {
      console.log('✅ Idempotency test PASSED - Duplicate submission detected');
      console.log(`   Original Job ID: ${firstJobId}`);
      console.log(`   Returned Job ID: ${secondResponse.data.data.jobId}`);
      return true;
    } else {
      console.log('❌ Idempotency test FAILED - Duplicate not detected');
      return false;
    }
    
  } catch (error) {
    console.log('❌ Assessment idempotency test failed:', error.response?.data?.error || error.message);
    return false;
  }
}

/**
 * Test 2: Assessment Submission without assessment_id (should generate one)
 */
async function testAssessmentIdGeneration() {
  console.log('\n🧪 Test 2: Assessment ID Generation');
  console.log('='.repeat(50));
  
  try {
    const assessmentPayload = {
      ...assessmentData,
      assessmentName: 'AI-Driven Talent Mapping'
      // No assessment_id provided
    };
    
    console.log('\n📤 Submitting assessment without assessment_id...');
    const response = await axios.post(
      `${config.apiGateway}/assessments/submit`,
      assessmentPayload,
      {
        headers: { Authorization: `Bearer ${authToken}` }
      }
    );
    
    console.log(`✅ Submission: ${response.status} - ${response.data.message}`);
    console.log(`   Job ID: ${response.data.data.jobId}`);
    console.log(`   Generated Assessment ID: ${response.data.data.assessmentId}`);
    
    // Check if assessment_id was generated
    if (response.data.data.assessmentId) {
      console.log('✅ Assessment ID generation test PASSED');
      return true;
    } else {
      console.log('❌ Assessment ID generation test FAILED - No assessment_id in response');
      return false;
    }
    
  } catch (error) {
    console.log('❌ Assessment ID generation test failed:', error.response?.data?.error || error.message);
    return false;
  }
}

/**
 * Test 3: Invalid assessment_id format
 */
async function testInvalidAssessmentId() {
  console.log('\n🧪 Test 3: Invalid Assessment ID Format');
  console.log('='.repeat(50));
  
  try {
    const assessmentPayload = {
      ...assessmentData,
      assessment_id: 'invalid-uuid-format',
      assessmentName: 'AI-Driven Talent Mapping'
    };
    
    console.log('\n📤 Submitting assessment with invalid assessment_id...');
    const response = await axios.post(
      `${config.apiGateway}/assessments/submit`,
      assessmentPayload,
      {
        headers: { Authorization: `Bearer ${authToken}` }
      }
    );
    
    // Should not reach here
    console.log('❌ Invalid assessment ID test FAILED - Request should have been rejected');
    return false;
    
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('✅ Invalid assessment ID test PASSED - Request properly rejected');
      console.log(`   Error: ${error.response.data.error.message}`);
      return true;
    } else {
      console.log('❌ Invalid assessment ID test FAILED - Unexpected error:', error.message);
      return false;
    }
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('🚀 Starting ATMA Idempotency and Retry Tests');
  console.log('='.repeat(60));
  
  // Setup
  const setupSuccess = await setupTestUser();
  if (!setupSuccess) {
    console.log('\n❌ Test setup failed. Exiting.');
    return;
  }
  
  // Run tests
  const results = {
    idempotency: await testAssessmentIdempotency(),
    idGeneration: await testAssessmentIdGeneration(),
    invalidId: await testInvalidAssessmentId()
  };
  
  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('='.repeat(30));
  console.log(`Assessment Idempotency: ${results.idempotency ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Assessment ID Generation: ${results.idGeneration ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Invalid Assessment ID: ${results.invalidId ? '✅ PASS' : '❌ FAIL'}`);
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All idempotency and retry tests PASSED!');
  } else {
    console.log('⚠️ Some tests FAILED. Please check the implementation.');
  }
}

// Run tests
runTests().catch(console.error);
