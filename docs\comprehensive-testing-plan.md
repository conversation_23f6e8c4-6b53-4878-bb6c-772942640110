# Rencana Testing Komprehensif - Idempotency & Retry Implementation

## Overview
Dokumen ini berisi rencana testing untuk memvalidasi implementasi idempotency dan retry mechanisms yang telah diimplementasikan dalam sistem ATMA Backend.

## Komponen yang Ditest

### 1. Assessment Service Idempotency
- ✅ **Implemented**: Idempotency middleware dengan client-generated assessment_id
- ✅ **Implemented**: Schema validation untuk assessment_id (UUID format)
- ✅ **Implemented**: Archive service integration untuk checking existing jobs

### 2. Analysis Worker Result Deduplication
- ✅ **Implemented**: Enhanced job deduplication service
- ✅ **Implemented**: Result existence checking sebelum processing
- ✅ **Implemented**: Integration dalam optimized assessment processor

### 3. Notification Service Duplicate Prevention
- ✅ **Implemented**: Notification deduplication service
- ✅ **Implemented**: Socket service integration dengan deduplication
- ✅ **Implemented**: Event consumer dengan deduplication checks

### 4. Enhanced Retry Mechanisms
- ✅ **Implemented**: Circuit breaker service
- ✅ **Implemented**: Enhanced error handler dengan categorization
- ✅ **Implemented**: Retry policies dengan exponential backoff

## Test Categories

### A. Unit Tests

#### A1. Assessment Service Tests
```bash
# Test file: assessment-service/tests/idempotency.test.js
```

**Test Cases:**
1. **Valid Assessment ID Submission**
   - Input: Assessment data dengan valid UUID assessment_id
   - Expected: Submission berhasil, assessment_id disimpan

2. **Duplicate Assessment ID Detection**
   - Input: Submit assessment dengan assessment_id yang sama 2x
   - Expected: Request kedua mengembalikan existing job, flag idempotent=true

3. **Auto-Generated Assessment ID**
   - Input: Assessment data tanpa assessment_id
   - Expected: System generate UUID, submission berhasil

4. **Invalid Assessment ID Format**
   - Input: Assessment data dengan assessment_id format invalid
   - Expected: Validation error 400, message format UUID required

5. **Assessment ID dengan User yang Berbeda**
   - Input: Assessment_id yang sama dari user berbeda
   - Expected: Kedua submission berhasil (assessment_id unique per user)

#### A2. Notification Deduplication Tests
```bash
# Test file: notification-service/tests/deduplication.test.js
```

**Test Cases:**
1. **Duplicate Notification Detection**
   - Input: Send notification yang sama 2x dalam threshold time
   - Expected: Notification kedua diblok

2. **Notification After Threshold**
   - Input: Send notification yang sama setelah threshold time
   - Expected: Notification kedua dikirim

3. **Different Notification Types**
   - Input: Send notification dengan type berbeda tapi data sama
   - Expected: Kedua notification dikirim

4. **Force Override**
   - Input: Send duplicate notification dengan forceOverride=true
   - Expected: Notification dikirim meskipun duplicate

#### A3. Circuit Breaker Tests
```bash
# Test file: analysis-worker/tests/circuit-breaker.test.js
```

**Test Cases:**
1. **Circuit Breaker Closed State**
   - Input: Normal operation calls
   - Expected: Calls berhasil, circuit tetap closed

2. **Circuit Breaker Open State**
   - Input: Multiple failures exceed threshold
   - Expected: Circuit open, subsequent calls blocked

3. **Circuit Breaker Half-Open State**
   - Input: Calls setelah recovery timeout
   - Expected: Limited calls allowed untuk testing

4. **Fallback Mechanism**
   - Input: Call saat circuit open dengan fallback
   - Expected: Fallback value/function executed

### B. Integration Tests

#### B1. End-to-End Assessment Flow
```bash
# Test file: test-e2e-assessment-idempotency.js
```

**Test Scenarios:**
1. **Complete Assessment Submission Flow**
   - Register user → Submit assessment → Check job creation
   - Verify assessment_id generated dan disimpan

2. **Idempotent Assessment Submission**
   - Submit assessment dengan assessment_id → Submit lagi
   - Verify response kedua menunjukkan existing job

3. **Assessment Processing dengan Result Deduplication**
   - Submit assessment → Wait processing → Check result
   - Submit duplicate → Verify existing result returned

#### B2. Notification Flow Testing
```bash
# Test file: test-e2e-notification-deduplication.js
```

**Test Scenarios:**
1. **Assessment Complete Notification**
   - Complete assessment → Check notification sent
   - Trigger duplicate event → Verify notification blocked

2. **Multiple Event Types**
   - Trigger analysis_started, analysis_completed, analysis_failed
   - Verify appropriate deduplication per event type

#### B3. Error Handling dan Retry Testing
```bash
# Test file: test-e2e-retry-mechanisms.js
```

**Test Scenarios:**
1. **Service Unavailable Retry**
   - Simulate service down → Verify retry attempts
   - Service back up → Verify eventual success

2. **Circuit Breaker Integration**
   - Multiple service failures → Verify circuit opens
   - Recovery → Verify circuit closes

### C. Load Tests

#### C1. Concurrent Assessment Submissions
```bash
# Test file: test-load-concurrent-assessments.js
```

**Test Scenarios:**
1. **Multiple Users Concurrent Submissions**
   - 50 users submit assessments simultaneously
   - Verify no duplicate processing, proper queuing

2. **Same User Multiple Submissions**
   - 1 user submit 10 assessments dengan assessment_id berbeda
   - Verify all processed correctly

3. **Duplicate Assessment_ID Load Test**
   - Multiple requests dengan assessment_id sama
   - Verify only one processed, others return existing

#### C2. Notification Load Testing
```bash
# Test file: test-load-notifications.js
```

**Test Scenarios:**
1. **High Volume Notifications**
   - Send 1000 notifications rapidly
   - Verify deduplication performance

2. **WebSocket Connection Load**
   - 100 concurrent WebSocket connections
   - Verify notification delivery dan deduplication

### D. Failure Simulation Tests

#### D1. Database Failure Scenarios
```bash
# Test file: test-failure-database.js
```

**Test Scenarios:**
1. **Archive Service Database Down**
   - Simulate DB connection failure
   - Verify circuit breaker activation, fallback behavior

2. **Partial Database Failure**
   - Simulate slow DB responses
   - Verify timeout handling, retry mechanisms

#### D2. Service Communication Failures
```bash
# Test file: test-failure-service-communication.js
```

**Test Scenarios:**
1. **Inter-Service Communication Failure**
   - Assessment Service → Archive Service failure
   - Verify retry attempts, circuit breaker

2. **Message Queue Failures**
   - RabbitMQ connection issues
   - Verify retry mechanisms, error handling

## Test Execution Plan

### Phase 1: Unit Tests (Estimasi: 2 hari)
1. Setup test environment
2. Implement unit tests untuk setiap komponen
3. Achieve 80%+ code coverage
4. Fix bugs yang ditemukan

### Phase 2: Integration Tests (Estimasi: 3 hari)
1. Setup integration test environment
2. Implement end-to-end test scenarios
3. Test dengan real database dan message queue
4. Validate idempotency dan retry behaviors

### Phase 3: Load Tests (Estimasi: 2 hari)
1. Setup load testing environment
2. Execute concurrent user scenarios
3. Monitor performance metrics
4. Optimize berdasarkan hasil

### Phase 4: Failure Tests (Estimasi: 2 hari)
1. Setup failure simulation tools
2. Test various failure scenarios
3. Validate recovery mechanisms
4. Document failure handling procedures

## Test Environment Setup

### Prerequisites
```bash
# Install dependencies
npm install --dev jest supertest
npm install --dev artillery  # untuk load testing
npm install --dev testcontainers  # untuk integration testing

# Setup test databases
docker-compose -f docker-compose.test.yml up -d
```

### Environment Variables
```env
# Test environment
NODE_ENV=test
TEST_DB_HOST=localhost
TEST_DB_PORT=5433
TEST_RABBITMQ_URL=amqp://localhost:5673

# Test timeouts
TEST_TIMEOUT=30000
CIRCUIT_BREAKER_TEST_THRESHOLD=3
NOTIFICATION_DEDUP_TEST_TTL=5000
```

### Test Data Setup
```bash
# Run migrations untuk test database
npm run test:db:migrate

# Seed test data
npm run test:db:seed
```

## Success Criteria

### Functional Requirements
- ✅ Assessment idempotency: 100% duplicate detection
- ✅ Notification deduplication: 95%+ duplicate prevention
- ✅ Circuit breaker: Response time < 100ms when open
- ✅ Retry mechanisms: 90%+ eventual success rate

### Performance Requirements
- Response time: < 500ms untuk idempotency checks
- Throughput: Handle 100 concurrent assessment submissions
- Memory usage: < 10% increase dengan deduplication
- Error rate: < 1% untuk normal operations

### Reliability Requirements
- Circuit breaker activation: < 5 seconds pada failure
- Recovery time: < 30 seconds setelah service restored
- Data consistency: 100% untuk idempotent operations
- Notification delivery: 99%+ success rate

## Test Execution Commands

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit
npm run test:integration
npm run test:load
npm run test:failure

# Run dengan coverage
npm run test:coverage

# Run specific test files
npm test -- test-idempotency-retry.js
npm test -- test-e2e-assessment-flow.js
```

## Monitoring dan Reporting

### Test Metrics
- Test coverage percentage
- Pass/fail rates per test category
- Performance benchmarks
- Error rates dan types

### Test Reports
- Daily test execution reports
- Performance trend analysis
- Failure pattern analysis
- Recommendations untuk improvements

## Next Steps

1. **Immediate (1-2 hari)**
   - Fix database migration issue
   - Implement basic unit tests
   - Validate core idempotency functionality

2. **Short-term (1 minggu)**
   - Complete integration test suite
   - Execute load testing scenarios
   - Performance optimization

3. **Medium-term (2 minggu)**
   - Comprehensive failure testing
   - Production readiness validation
   - Documentation completion
