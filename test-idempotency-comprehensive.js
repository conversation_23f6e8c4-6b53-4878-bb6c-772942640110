/**
 * Comprehensive Idempotency and Retry Testing Script
 * Tests all implemented idempotency and retry mechanisms
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// Test configuration
const config = {
  apiGateway: 'http://localhost:3000',
  notificationService: 'http://localhost:3005',
  testTimeout: 30000,
  retryDelay: 1000
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

// Sample assessment data
const assessmentData = {
  riasec: { realistic: 4, investigative: 5, artistic: 3, social: 4, enterprising: 2, conventional: 3 },
  ocean: { openness: 4, conscientiousness: 5, extraversion: 3, agreeableness: 4, neuroticism: 2 },
  viaIs: {
    creativity: 4, curiosity: 5, judgment: 4, love_of_learning: 5, perspective: 4,
    bravery: 3, perseverance: 4, honesty: 5, zest: 3, love: 4, kindness: 4,
    social_intelligence: 3, teamwork: 4, fairness: 5, leadership: 3,
    forgiveness: 4, humility: 4, prudence: 5, self_regulation: 4,
    appreciation_of_beauty: 3, gratitude: 4, hope: 4, humor: 3, spirituality: 3
  }
};

/**
 * Test helper functions
 */
function logTest(testName, status, details = '') {
  const result = status ? '✅ PASS' : '❌ FAIL';
  console.log(`${result} ${testName}`);
  if (details) console.log(`   ${details}`);
  
  testResults.total++;
  if (status) testResults.passed++;
  else testResults.failed++;
  
  testResults.details.push({ testName, status, details });
}

async function createTestUser() {
  const userData = {
    email: `testuser_${Date.now()}_${Math.floor(Math.random() * 10000)}@example.com`,
    password: 'TestPassword123!',
    username: `testuser${Date.now()}${Math.floor(Math.random() * 10000)}`
  };
  
  try {
    const response = await axios.post(`${config.apiGateway}/auth/register`, userData);
    if (response.data.success) {
      return {
        token: response.data.data.token,
        userId: response.data.data.user.id,
        email: userData.email
      };
    }
  } catch (error) {
    console.log('Failed to create test user:', error.response?.data || error.message);
  }
  return null;
}

/**
 * Test 1: Assessment Schema Validation
 */
async function testAssessmentSchemaValidation(authToken) {
  console.log('\n🧪 Test 1: Assessment Schema Validation');
  console.log('='.repeat(50));
  
  // Test 1.1: Valid assessment_id
  try {
    const validPayload = {
      ...assessmentData,
      assessment_id: uuidv4(),
      assessmentName: 'AI-Driven Talent Mapping'
    };
    
    const response = await axios.post(
      `${config.apiGateway}/assessments/submit`,
      validPayload,
      { headers: { Authorization: `Bearer ${authToken}` } }
    );
    
    logTest('1.1 Valid assessment_id format', 
      response.status === 202 && response.data.data.assessmentId,
      `Assessment ID: ${response.data.data.assessmentId}`);
      
  } catch (error) {
    logTest('1.1 Valid assessment_id format', false, error.response?.data?.error?.message || error.message);
  }
  
  // Test 1.2: Invalid assessment_id format
  try {
    const invalidPayload = {
      ...assessmentData,
      assessment_id: 'invalid-uuid-format',
      assessmentName: 'AI-Driven Talent Mapping'
    };
    
    await axios.post(
      `${config.apiGateway}/assessments/submit`,
      invalidPayload,
      { headers: { Authorization: `Bearer ${authToken}` } }
    );
    
    logTest('1.2 Invalid assessment_id rejection', false, 'Should have been rejected');
    
  } catch (error) {
    logTest('1.2 Invalid assessment_id rejection', 
      error.response?.status === 400,
      error.response?.data?.error?.message || 'Properly rejected');
  }
  
  // Test 1.3: Auto-generated assessment_id
  try {
    const noIdPayload = {
      ...assessmentData,
      assessmentName: 'AI-Driven Talent Mapping'
    };
    
    const response = await axios.post(
      `${config.apiGateway}/assessments/submit`,
      noIdPayload,
      { headers: { Authorization: `Bearer ${authToken}` } }
    );
    
    logTest('1.3 Auto-generated assessment_id', 
      response.status === 202 && response.data.data.assessmentId,
      `Generated ID: ${response.data.data.assessmentId}`);
      
  } catch (error) {
    logTest('1.3 Auto-generated assessment_id', false, error.response?.data?.error?.message || error.message);
  }
}

/**
 * Test 2: Idempotency Mechanism
 */
async function testIdempotencyMechanism(authToken) {
  console.log('\n🧪 Test 2: Idempotency Mechanism');
  console.log('='.repeat(50));
  
  const assessmentId = uuidv4();
  const payload = {
    ...assessmentData,
    assessment_id: assessmentId,
    assessmentName: 'AI-Driven Talent Mapping'
  };
  
  try {
    // First submission
    const firstResponse = await axios.post(
      `${config.apiGateway}/assessments/submit`,
      payload,
      { headers: { Authorization: `Bearer ${authToken}` } }
    );
    
    logTest('2.1 First submission success', 
      firstResponse.status === 202,
      `Job ID: ${firstResponse.data.data.jobId}`);
    
    const firstJobId = firstResponse.data.data.jobId;
    
    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, config.retryDelay));
    
    // Second submission (should be idempotent)
    const secondResponse = await axios.post(
      `${config.apiGateway}/assessments/submit`,
      payload,
      { headers: { Authorization: `Bearer ${authToken}` } }
    );
    
    const isIdempotent = secondResponse.data.data.idempotent === true;
    logTest('2.2 Idempotent duplicate detection', 
      isIdempotent,
      isIdempotent ? 'Duplicate properly detected' : 'Duplicate not detected');
    
    // Test different user with same assessment_id
    const otherUser = await createTestUser();
    if (otherUser) {
      const thirdResponse = await axios.post(
        `${config.apiGateway}/assessments/submit`,
        payload,
        { headers: { Authorization: `Bearer ${otherUser.token}` } }
      );
      
      logTest('2.3 Different user same assessment_id', 
        thirdResponse.status === 202 && !thirdResponse.data.data.idempotent,
        'Different users can use same assessment_id');
    } else {
      logTest('2.3 Different user same assessment_id', false, 'Could not create second user');
    }
    
  } catch (error) {
    logTest('2.x Idempotency test error', false, error.response?.data?.error?.message || error.message);
  }
}

/**
 * Test 3: Service Health and Circuit Breaker
 */
async function testServiceHealthAndCircuitBreaker() {
  console.log('\n🧪 Test 3: Service Health and Circuit Breaker');
  console.log('='.repeat(50));
  
  // Test 3.1: Health check endpoints
  const services = [
    { name: 'API Gateway', url: `${config.apiGateway}/health` },
    { name: 'Notification Service', url: `${config.notificationService}/health` }
  ];
  
  for (const service of services) {
    try {
      const response = await axios.get(service.url, { timeout: 5000 });
      logTest(`3.1 ${service.name} health check`, 
        response.status === 200,
        `Status: ${response.data.status || 'healthy'}`);
    } catch (error) {
      logTest(`3.1 ${service.name} health check`, false, 'Service unavailable');
    }
  }
  
  // Test 3.2: Error handling
  try {
    // Try to access non-existent endpoint to test error handling
    await axios.get(`${config.apiGateway}/non-existent-endpoint`);
    logTest('3.2 Error handling', false, 'Should have returned 404');
  } catch (error) {
    logTest('3.2 Error handling', 
      error.response?.status === 404,
      `Proper 404 response: ${error.response?.status}`);
  }
}

/**
 * Test 4: Notification Deduplication (if notification service is available)
 */
async function testNotificationDeduplication() {
  console.log('\n🧪 Test 4: Notification Deduplication');
  console.log('='.repeat(50));
  
  try {
    // Test notification service stats endpoint
    const statsResponse = await axios.get(`${config.notificationService}/stats`);
    logTest('4.1 Notification service stats', 
      statsResponse.status === 200,
      `Active connections: ${statsResponse.data.connections?.total || 0}`);
      
    // Test WebSocket endpoint availability
    const wsResponse = await axios.get(`${config.notificationService}/`);
    logTest('4.2 WebSocket endpoint availability', 
      wsResponse.status === 200,
      'WebSocket server accessible');
      
  } catch (error) {
    logTest('4.x Notification service tests', false, 'Service not accessible for testing');
  }
}

/**
 * Test 5: Performance and Load Testing (Basic)
 */
async function testBasicPerformance(authToken) {
  console.log('\n🧪 Test 5: Basic Performance Testing');
  console.log('='.repeat(50));
  
  // Test 5.1: Multiple concurrent requests
  const concurrentRequests = 5;
  const promises = [];
  
  for (let i = 0; i < concurrentRequests; i++) {
    const payload = {
      ...assessmentData,
      assessment_id: uuidv4(),
      assessmentName: 'AI-Driven Talent Mapping'
    };
    
    promises.push(
      axios.post(
        `${config.apiGateway}/assessments/submit`,
        payload,
        { headers: { Authorization: `Bearer ${authToken}` } }
      )
    );
  }
  
  try {
    const startTime = Date.now();
    const responses = await Promise.all(promises);
    const endTime = Date.now();
    
    const allSuccessful = responses.every(r => r.status === 202);
    const avgResponseTime = (endTime - startTime) / concurrentRequests;
    
    logTest('5.1 Concurrent submissions', 
      allSuccessful,
      `${concurrentRequests} requests, avg ${avgResponseTime.toFixed(2)}ms`);
      
  } catch (error) {
    logTest('5.1 Concurrent submissions', false, 'Some requests failed');
  }
  
  // Test 5.2: Response time check
  try {
    const startTime = Date.now();
    await axios.get(`${config.apiGateway}/health`);
    const responseTime = Date.now() - startTime;
    
    logTest('5.2 Response time check', 
      responseTime < 1000,
      `Health check: ${responseTime}ms`);
      
  } catch (error) {
    logTest('5.2 Response time check', false, 'Health check failed');
  }
}

/**
 * Main test runner
 */
async function runComprehensiveTests() {
  console.log('🚀 Starting Comprehensive Idempotency and Retry Tests');
  console.log('='.repeat(70));
  
  // Setup test user
  console.log('\n🔧 Setting up test environment...');
  const testUser = await createTestUser();
  
  if (!testUser) {
    console.log('❌ Failed to setup test environment. Exiting.');
    return;
  }
  
  console.log(`✅ Test user created: ${testUser.email}`);
  console.log(`   User ID: ${testUser.userId}`);
  
  // Run test suites
  await testAssessmentSchemaValidation(testUser.token);
  await testIdempotencyMechanism(testUser.token);
  await testServiceHealthAndCircuitBreaker();
  await testNotificationDeduplication();
  await testBasicPerformance(testUser.token);
  
  // Test summary
  console.log('\n📊 Test Results Summary');
  console.log('='.repeat(30));
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed} ✅`);
  console.log(`Failed: ${testResults.failed} ❌`);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.details
      .filter(t => !t.status)
      .forEach(t => console.log(`   - ${t.testName}: ${t.details}`));
  }
  
  console.log('\n🎯 Overall Result:', testResults.failed === 0 ? '✅ ALL TESTS PASSED' : '⚠️ SOME TESTS FAILED');
  
  // Recommendations
  console.log('\n💡 Recommendations:');
  if (testResults.failed === 0) {
    console.log('   - All core functionality is working correctly');
    console.log('   - Ready for production deployment');
    console.log('   - Consider running load tests for production readiness');
  } else {
    console.log('   - Review failed tests and fix implementation issues');
    console.log('   - Ensure all services are running and properly configured');
    console.log('   - Check database connectivity and migrations');
  }
}

// Run tests
runComprehensiveTests().catch(console.error);
