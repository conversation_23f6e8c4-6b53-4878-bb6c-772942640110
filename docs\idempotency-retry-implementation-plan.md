# Rencana Implementasi Idempotency & Retry Mechanism

## Overview
Dokumen ini menjelaskan rencana implementasi untuk memastikan idempotency dan retry mechanism yang robust di sistem ATMA Backend untuk mencegah duplikasi data dan meningkatkan reliability.

## 1. Idempotency Implementation

### 1.1 AssessmentService - Create Assessment

**Tujuan**: Mencegah duplikasi assessment submission ketika client mengirim request yang sama berulang kali.

**File yang Terpengaruh**:
- `assessment-service/src/routes/assessments.js` (endpoint `/submit`)
- `assessment-service/src/services/archiveService.js`
- `assessment-service/src/middleware/idempotency.js` (baru)
- `assessment-service/src/schemas/assessment.js`

**Implementasi**:

#### A. Client-Generated Assessment ID
```javascript
// Tambah field assessment_id di schema
const assessmentSchema = Joi.object({
  assessment_id: Joi.string().uuid().optional(), // Client-generated UUID
  riasec: riasecSchema.required(),
  ocean: oceanSchema.required(),
  viaIs: viaIsSchema.required(),
  assessmentName: Joi.string().valid(...).optional()
});
```

#### B. Idempotency Middleware
```javascript
// assessment-service/src/middleware/idempotency.js
const checkAssessmentIdempotency = async (req, res, next) => {
  const { assessment_id } = req.body;
  const { id: userId } = req.user;
  
  if (assessment_id) {
    // Check if assessment already exists
    const existing = await archiveService.getJobByAssessmentId(assessment_id, userId);
    if (existing) {
      return res.status(200).json({
        success: true,
        message: 'Assessment already submitted',
        data: existing,
        idempotent: true
      });
    }
  } else {
    // Generate assessment_id if not provided
    req.body.assessment_id = uuidv4();
  }
  
  next();
};
```

#### C. Archive Service Enhancement
```javascript
// assessment-service/src/services/archiveService.js
const createJob = async (jobId, userId, assessmentData, assessmentName, assessmentId) => {
  const response = await archiveClient.post('/jobs', {
    job_id: jobId,
    assessment_id: assessmentId, // Tambah field ini
    user_id: userId,
    assessment_data: assessmentData,
    assessment_name: assessmentName,
    status: 'queued'
  });
  return response.data;
};

const getJobByAssessmentId = async (assessmentId, userId) => {
  try {
    const response = await archiveClient.get(`/jobs/by-assessment-id/${assessmentId}`, {
      params: { user_id: userId }
    });
    return response.data.data;
  } catch (error) {
    if (error.response?.status === 404) return null;
    throw error;
  }
};
```

### 1.2 AnalysisWorker - Result Storage

**Tujuan**: Mencegah duplikasi hasil analisis ketika worker memproses job yang sama berulang kali.

**File yang Terpengaruh**:
- `analysis-worker/src/processors/assessmentProcessor.js`
- `analysis-worker/src/services/archiveService.js`
- `analysis-worker/src/services/jobDeduplicationService.js` (sudah ada, perlu enhancement)

**Implementasi**:

#### A. Enhanced Job Deduplication
```javascript
// analysis-worker/src/services/jobDeduplicationService.js
class JobDeduplicationService {
  checkResultExists(jobId, userId) {
    // Check if result already exists in Archive Service
    return archiveService.getAnalysisResult(jobId, userId);
  }
  
  generateResultHash(userId, assessmentData, personaProfile) {
    const data = {
      userId,
      assessmentData: this.normalizeAssessmentData(assessmentData),
      archetype: personaProfile.archetype
    };
    return crypto.createHash('sha256').update(JSON.stringify(data)).digest('hex');
  }
}
```

#### B. Archive Service Result Check
```javascript
// analysis-worker/src/services/archiveService.js
const saveAnalysisResult = async (userId, assessmentData, personaProfile, jobId, assessmentName) => {
  // Check if result already exists
  const existingResult = await getAnalysisResult(jobId, userId);
  if (existingResult) {
    logger.info('Analysis result already exists, returning existing', {
      jobId, userId, resultId: existingResult.id
    });
    return existingResult;
  }
  
  // Proceed with normal save
  const response = await archiveClient.post('/results', {
    user_id: userId,
    job_id: jobId, // Tambah job_id untuk tracking
    assessment_data: assessmentData,
    persona_profile: personaProfile,
    assessment_name: assessmentName,
    status: 'completed'
  });
  
  return response.data.data;
};

const getAnalysisResult = async (jobId, userId) => {
  try {
    const response = await archiveClient.get(`/results/by-job/${jobId}`, {
      params: { user_id: userId }
    });
    return response.data.data;
  } catch (error) {
    if (error.response?.status === 404) return null;
    throw error;
  }
};
```

### 1.3 NotificationService - Duplicate Prevention

**Tujuan**: Mencegah user menerima notifikasi duplikat untuk job yang sama.

**File yang Terpengaruh**:
- `notification-service/src/services/socketService.js`
- `notification-service/src/services/notificationDeduplication.js` (baru)
- `notification-service/src/services/eventConsumer.js`

**Implementasi**:

#### A. Notification Deduplication Service
```javascript
// notification-service/src/services/notificationDeduplication.js
class NotificationDeduplicationService {
  constructor() {
    this.sentNotifications = new Map(); // In production: use Redis
    this.retentionMs = 24 * 60 * 60 * 1000; // 24 hours
  }
  
  generateNotificationKey(userId, jobId, eventType) {
    return `${userId}:${jobId}:${eventType}`;
  }
  
  isDuplicate(userId, jobId, eventType) {
    const key = this.generateNotificationKey(userId, jobId, eventType);
    const existing = this.sentNotifications.get(key);
    
    if (existing && (Date.now() - existing.timestamp) < this.retentionMs) {
      return true;
    }
    
    return false;
  }
  
  markAsSent(userId, jobId, eventType, metadata = {}) {
    const key = this.generateNotificationKey(userId, jobId, eventType);
    this.sentNotifications.set(key, {
      timestamp: Date.now(),
      metadata
    });
  }
}
```

#### B. Enhanced Socket Service
```javascript
// notification-service/src/services/socketService.js
const notificationDedup = require('./notificationDeduplication');

class SocketService {
  sendToUser(userId, event, data) {
    const { jobId } = data;
    
    // Check for duplicate notification
    if (jobId && notificationDedup.isDuplicate(userId, jobId, event)) {
      logger.info('Duplicate notification prevented', { userId, jobId, event });
      return false;
    }
    
    const room = `user:${userId}`;
    const socketCount = this.io.sockets.adapter.rooms.get(room)?.size || 0;
    
    if (socketCount > 0) {
      this.io.to(room).emit(event, {
        ...data,
        timestamp: new Date().toISOString()
      });
      
      // Mark as sent
      if (jobId) {
        notificationDedup.markAsSent(userId, jobId, event, data);
      }
      
      return true;
    }
    
    return false;
  }
}
```

## 2. Retry Mechanism Enhancement

### 2.1 Worker-Level Retry Policy

**File yang Terpengaruh**:
- `analysis-worker/src/services/queueConsumer.js`
- `analysis-worker/src/utils/errorHandler.js`
- `analysis-worker/src/config/retry.js` (baru)

**Implementasi**:

#### A. Retry Configuration
```javascript
// analysis-worker/src/config/retry.js
const RETRY_CONFIG = {
  maxRetries: parseInt(process.env.MAX_RETRIES || '3'),
  baseDelay: parseInt(process.env.RETRY_DELAY || '5000'),
  maxDelay: parseInt(process.env.MAX_RETRY_DELAY || '60000'),
  backoffMultiplier: parseFloat(process.env.BACKOFF_MULTIPLIER || '2.0'),
  
  // Retry policies per operation type
  policies: {
    AI_SERVICE: {
      maxRetries: 5,
      baseDelay: 2000,
      shouldRetry: (error) => {
        return error.code === 'AI_SERVICE_ERROR' ||
               error.code === 'RATE_LIMIT_ERROR' ||
               error.message.includes('timeout');
      }
    },
    ARCHIVE_SERVICE: {
      maxRetries: 3,
      baseDelay: 1000,
      shouldRetry: (error) => error.isRetryable
    },
    NOTIFICATION_SERVICE: {
      maxRetries: 2,
      baseDelay: 500,
      shouldRetry: () => true // Always retry notifications
    }
  }
};
```

#### B. Enhanced Error Handler
```javascript
// analysis-worker/src/utils/errorHandler.js
const withRetryPolicy = async (operation, policyName, customOptions = {}) => {
  const policy = RETRY_CONFIG.policies[policyName] || RETRY_CONFIG;
  const options = { ...policy, ...customOptions };
  
  return withRetry(operation, options);
};

// Circuit breaker pattern
class CircuitBreaker {
  constructor(threshold = 5, timeout = 60000) {
    this.failureThreshold = threshold;
    this.timeout = timeout;
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
  }
  
  async execute(operation) {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }
    
    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  onSuccess() {
    this.failureCount = 0;
    this.state = 'CLOSED';
  }
  
  onFailure() {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    if (this.failureCount >= this.failureThreshold) {
      this.state = 'OPEN';
    }
  }
}
```

### 2.2 Dead Letter Queue Implementation

**File yang Terpengaruh**:
- `analysis-worker/src/config/rabbitmq.js`
- `analysis-worker/src/services/dlqProcessor.js` (baru)

**Implementasi**:

#### A. DLQ Configuration
```javascript
// analysis-worker/src/config/rabbitmq.js
const setupQueues = async (channel) => {
  // Main queue with DLQ
  await channel.assertQueue(config.queue, {
    durable: true,
    arguments: {
      'x-dead-letter-exchange': `${config.exchange}_dlx`,
      'x-dead-letter-routing-key': 'failed',
      'x-message-ttl': 3600000 // 1 hour TTL
    }
  });
  
  // Dead Letter Exchange
  await channel.assertExchange(`${config.exchange}_dlx`, 'direct', { durable: true });
  
  // Dead Letter Queue
  await channel.assertQueue(`${config.queue}_dlq`, { durable: true });
  await channel.bindQueue(`${config.queue}_dlq`, `${config.exchange}_dlx`, 'failed');
};
```

## 3. Database Schema Changes

### 3.1 Archive Service Database

**File yang Terpengaruh**:
- `archive-service/src/models/AnalysisJob.js`
- `archive-service/src/models/AnalysisResult.js`
- `archive-service/migrations/` (baru)

**Schema Changes**:

```sql
-- Add assessment_id to analysis_jobs table
ALTER TABLE analysis_jobs ADD COLUMN assessment_id VARCHAR(36) UNIQUE;
CREATE INDEX idx_analysis_jobs_assessment_id ON analysis_jobs(assessment_id);

-- Add job_id to analysis_results table for better tracking
ALTER TABLE analysis_results ADD COLUMN job_id VARCHAR(36);
CREATE INDEX idx_analysis_results_job_id ON analysis_results(job_id);

-- Add retry tracking
ALTER TABLE analysis_jobs ADD COLUMN retry_count INTEGER DEFAULT 0;
ALTER TABLE analysis_jobs ADD COLUMN last_retry_at TIMESTAMP NULL;
```

## 4. Monitoring & Observability

### 4.1 Metrics Collection

**File yang Terpengaruh**:
- `analysis-worker/src/services/metricsCollector.js` (baru)
- `assessment-service/src/middleware/metrics.js` (baru)

**Implementasi**:
- Retry attempt counters
- Idempotency hit rates
- Circuit breaker state changes
- DLQ message counts
- Processing time distributions

## 5. Testing Strategy

### 5.1 Test Cases

**File yang Terpengaruh**:
- `tests/integration/idempotency.test.js` (baru)
- `tests/unit/retry.test.js` (baru)
- `tests/load/duplicate-requests.test.js` (baru)

**Test Scenarios**:
1. Duplicate assessment submission
2. Worker retry on AI service failure
3. Notification deduplication
4. Circuit breaker behavior
5. DLQ message processing

## 6. Configuration

### 6.1 Environment Variables

```bash
# Idempotency
ENABLE_IDEMPOTENCY=true
IDEMPOTENCY_CACHE_TTL=3600000

# Retry
MAX_RETRIES=3
RETRY_DELAY=5000
MAX_RETRY_DELAY=60000
BACKOFF_MULTIPLIER=2.0

# Circuit Breaker
CIRCUIT_BREAKER_THRESHOLD=5
CIRCUIT_BREAKER_TIMEOUT=60000

# Notification Deduplication
NOTIFICATION_DEDUP_TTL=86400000
```

## 7. Implementation Priority

1. **Phase 1**: AssessmentService idempotency (client-generated ID)
2. **Phase 2**: AnalysisWorker result deduplication
3. **Phase 3**: NotificationService duplicate prevention
4. **Phase 4**: Enhanced retry mechanisms with circuit breaker
5. **Phase 5**: DLQ implementation and monitoring

## 8. Risks & Considerations

### 8.1 Potential Issues
- **Memory Usage**: In-memory caches perlu diganti dengan Redis di production
- **Race Conditions**: Perlu atomic operations untuk check-and-set
- **Clock Skew**: TTL bisa bermasalah jika server time tidak sync
- **Storage Overhead**: Assessment ID dan job tracking menambah storage

### 8.2 Mitigations
- Implement Redis untuk distributed caching
- Use database constraints untuk prevent race conditions
- Monitor dan alert untuk retry rates yang tinggi
- Implement cleanup jobs untuk old cache entries

## 9. Success Metrics

- **Idempotency Rate**: % requests yang di-handle secara idempotent
- **Retry Success Rate**: % retries yang berhasil
- **Duplicate Prevention**: % notifikasi duplikat yang dicegah
- **Processing Time**: Impact terhadap latency
- **Error Rates**: Reduction dalam error rates setelah implementasi
