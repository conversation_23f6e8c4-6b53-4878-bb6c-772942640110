/**
 * RabbitMQ Configuration for Archive Service
 * Event Consumer Configuration for Event-Driven Architecture
 */

const amqp = require('amqplib');
const logger = require('../utils/logger');

// RabbitMQ configuration
const config = {
  url: process.env.RABBITMQ_URL || 'amqp://localhost:5672',
  // Events configuration for event-driven architecture
  eventsExchange: process.env.EVENTS_EXCHANGE_NAME || 'atma_events_exchange',
  archiveQueue: process.env.EVENTS_QUEUE_NAME_ARCHIVE || 'analysis_events_archive',
  archiveDeadLetterQueue: process.env.EVENTS_QUEUE_NAME_ARCHIVE_DLQ || 'analysis_events_archive_dlq',
  eventsRoutingKeys: {
    analysisCompleted: 'analysis.completed',
    analysisFailed: 'analysis.failed',
    analysisStarted: 'analysis.started'
  },
  consumerOptions: {
    prefetch: parseInt(process.env.CONSUMER_PREFETCH || '10'),
    durable: true,
    persistent: true
  }
};

// Connection and channel variables
let connection = null;
let channel = null;

/**
 * Initialize RabbitMQ connection and setup exchange/queue for event consumption
 * @returns {Promise<Object>} - RabbitMQ channel
 */
const initialize = async () => {
  try {
    // Create connection
    logger.info('Archive Service connecting to RabbitMQ...', {
      url: config.url.replace(/\/\/.*@/, '//***@') // Hide credentials in logs
    });
    
    connection = await amqp.connect(config.url);

    // Handle connection close
    connection.on('close', () => {
      logger.warn('Archive Service RabbitMQ connection closed');
      setTimeout(reconnect, 5000);
    });

    // Handle connection error
    connection.on('error', (err) => {
      logger.error('Archive Service RabbitMQ connection error', { error: err.message });
      setTimeout(reconnect, 5000);
    });

    // Create channel
    channel = await connection.createChannel();

    // Set prefetch count for fair dispatch
    await channel.prefetch(config.consumerOptions.prefetch);

    // Setup events exchange (should already exist, but assert for safety)
    await channel.assertExchange(config.eventsExchange, 'topic', {
      durable: config.consumerOptions.durable
    });

    // Setup archive queue with dead letter queue
    await channel.assertQueue(config.archiveQueue, {
      durable: config.consumerOptions.durable,
      arguments: {
        'x-dead-letter-exchange': config.eventsExchange,
        'x-dead-letter-routing-key': 'archive.dlq'
      }
    });

    // Setup dead letter queue for failed archive events
    await channel.assertQueue(config.archiveDeadLetterQueue, {
      durable: config.consumerOptions.durable
    });

    // Bind archive queue to events exchange for all analysis events
    await channel.bindQueue(config.archiveQueue, config.eventsExchange, config.eventsRoutingKeys.analysisCompleted);
    await channel.bindQueue(config.archiveQueue, config.eventsExchange, config.eventsRoutingKeys.analysisFailed);
    await channel.bindQueue(config.archiveQueue, config.eventsExchange, config.eventsRoutingKeys.analysisStarted);

    // Bind dead letter queue
    await channel.bindQueue(config.archiveDeadLetterQueue, config.eventsExchange, 'archive.dlq');

    logger.info('Archive Service RabbitMQ connected and configured', {
      archiveQueue: config.archiveQueue,
      eventsExchange: config.eventsExchange,
      deadLetterQueue: config.archiveDeadLetterQueue,
      routingKeys: Object.values(config.eventsRoutingKeys),
      prefetch: config.consumerOptions.prefetch
    });

    return channel;
  } catch (error) {
    logger.error('Failed to initialize Archive Service RabbitMQ', { error: error.message });
    throw error;
  }
};

/**
 * Reconnect to RabbitMQ
 */
const reconnect = async () => {
  try {
    logger.info('Attempting to reconnect Archive Service to RabbitMQ...');
    
    if (connection) {
      try {
        await connection.close();
      } catch (err) {
        logger.error('Error closing existing Archive Service RabbitMQ connection', { error: err.message });
      }
    }

    await initialize();
    logger.info('Archive Service RabbitMQ reconnected successfully');
  } catch (error) {
    logger.error('Failed to reconnect Archive Service to RabbitMQ', { error: error.message });
    setTimeout(reconnect, 5000);
  }
};

/**
 * Get RabbitMQ channel (initialize if needed)
 * @returns {Promise<Object>} - RabbitMQ channel
 */
const getChannel = async () => {
  if (!channel) {
    await initialize();
  }
  return channel;
};

/**
 * Close RabbitMQ connection
 */
const close = async () => {
  try {
    if (channel) {
      await channel.close();
    }
    if (connection) {
      await connection.close();
    }
    logger.info('Archive Service RabbitMQ connection closed gracefully');
  } catch (error) {
    logger.error('Error closing Archive Service RabbitMQ connection', { error: error.message });
    throw error;
  }
};

/**
 * Check if RabbitMQ connection is healthy
 * @returns {Promise<boolean>} - Connection status
 */
const checkHealth = async () => {
  try {
    if (!connection || !channel) {
      return false;
    }

    // Check if connection is still open
    return connection.connection && !connection.connection.closed;
  } catch (error) {
    logger.error('Error checking Archive Service RabbitMQ health', { error: error.message });
    return false;
  }
};

/**
 * Get configuration for monitoring/debugging
 * @returns {Object} - Configuration object
 */
const getConfig = () => {
  return {
    ...config,
    isConnected: !!connection && !!channel,
    connectionStatus: connection?.connection?.closed === false ? 'open' : 'closed'
  };
};

module.exports = {
  config,
  initialize,
  getChannel,
  close,
  checkHealth,
  getConfig
};
