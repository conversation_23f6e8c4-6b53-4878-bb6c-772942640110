/**
 * Health Check Routes
 * Provides health status and system information
 */

const express = require('express');
const { testConnection } = require('../config/database');
const eventConsumer = require('../services/eventConsumer');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * GET /health
 * Basic health check endpoint
 */
router.get('/health', async (req, res) => {
  try {
    // Test database connection
    const isDatabaseConnected = await testConnection();

    // Test event consumer health
    const isEventConsumerHealthy = await eventConsumer.checkHealth();

    const isHealthy = isDatabaseConnected && (isEventConsumerHealthy || !process.env.RABBITMQ_URL);

    const healthStatus = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      database: isDatabaseConnected ? 'connected' : 'disconnected',
      eventConsumer: process.env.RABBITMQ_URL ?
        (isEventConsumerHealthy ? 'healthy' : 'unhealthy') : 'disabled',
      version: '1.0.0',
      service: 'archive-service'
    };

    // Log health check
    logger.info('Health check performed', {
      status: healthStatus.status,
      database: healthStatus.database,
      eventConsumer: healthStatus.eventConsumer
    });

    // Return appropriate status code
    const statusCode = isHealthy ? 200 : 503;
    res.status(statusCode).json(healthStatus);

  } catch (error) {
    logger.error('Health check failed', { error: error.message });

    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      database: 'error',
      eventConsumer: 'error',
      version: '1.0.0',
      service: 'archive-service',
      error: error.message
    });
  }
});

/**
 * GET /health/detailed
 * Detailed health check with more information
 */
router.get('/health/detailed', async (req, res) => {
  try {
    const isDatabaseConnected = await testConnection();
    const isEventConsumerHealthy = await eventConsumer.checkHealth();
    const eventConsumerStatus = eventConsumer.getStatus();

    const isHealthy = isDatabaseConnected && (isEventConsumerHealthy || !process.env.RABBITMQ_URL);

    const detailedHealth = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      service: 'archive-service',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      database: {
        status: isDatabaseConnected ? 'connected' : 'disconnected',
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 5432,
        database: process.env.DB_NAME || 'atma_db',
        schema: process.env.DB_SCHEMA || 'archive'
      },
      eventConsumer: {
        enabled: !!process.env.RABBITMQ_URL,
        status: process.env.RABBITMQ_URL ?
          (isEventConsumerHealthy ? 'healthy' : 'unhealthy') : 'disabled',
        ...eventConsumerStatus
      }
    };

    const statusCode = isHealthy ? 200 : 503;
    res.status(statusCode).json(detailedHealth);

  } catch (error) {
    logger.error('Detailed health check failed', { error: error.message });

    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      service: 'archive-service',
      error: error.message
    });
  }
});

module.exports = router;
