# Server Configuration
PORT=3002
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_db
DB_USER=postgres
DB_PASSWORD=password
DB_DIALECT=postgres
DB_SCHEMA=archive

# JWT Configuration (for token verification)
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production

# Auth Service Configuration
AUTH_SERVICE_URL=http://localhost:3001

# Internal Service Configuration
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# Pagination Configuration
DEFAULT_PAGE_SIZE=10
MAX_PAGE_SIZE=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/archive-service.log

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# Event-Driven Architecture Configuration
RABBITMQ_URL=amqp://localhost:5672
EVENTS_EXCHANGE_NAME=atma_events_exchange
EVENTS_QUEUE_NAME_ARCHIVE=analysis_events_archive
CONSUMER_PREFETCH=10

# Batch Processing Configuration
BATCH_MAX_SIZE=50
BATCH_TIMEOUT=2000
BATCH_MAX_QUEUE_SIZE=1000
