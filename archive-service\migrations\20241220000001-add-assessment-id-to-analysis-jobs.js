'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add assessment_id column to analysis_jobs table
    await queryInterface.addColumn('analysis_jobs', 'assessment_id', {
      type: Sequelize.UUID,
      allowNull: true,
      unique: true,
      after: 'job_id' // Position after job_id column
    });

    // Add index for assessment_id
    await queryInterface.addIndex('analysis_jobs', ['assessment_id'], {
      unique: true,
      name: 'idx_analysis_jobs_assessment_id'
    });

    console.log('✅ Added assessment_id column and index to analysis_jobs table');
  },

  async down(queryInterface, Sequelize) {
    // Remove index first
    await queryInterface.removeIndex('analysis_jobs', 'idx_analysis_jobs_assessment_id');
    
    // Remove assessment_id column
    await queryInterface.removeColumn('analysis_jobs', 'assessment_id');

    console.log('✅ Removed assessment_id column and index from analysis_jobs table');
  }
};
