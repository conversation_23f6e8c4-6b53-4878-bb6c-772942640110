/**
 * Circuit Breaker Service
 * Implements circuit breaker pattern for external service calls
 * Prevents cascading failures and provides fallback mechanisms
 */

const logger = require('../utils/logger');

// Circuit breaker states
const STATES = {
  CLOSED: 'CLOSED',       // Normal operation
  OPEN: 'OPEN',           // Circuit is open, calls are blocked
  HALF_OPEN: 'HALF_OPEN'  // Testing if service is back up
};

class CircuitBreaker {
  constructor(name, options = {}) {
    this.name = name;
    this.state = STATES.CLOSED;
    
    // Configuration
    this.failureThreshold = options.failureThreshold || 5;
    this.recoveryTimeout = options.recoveryTimeout || 60000; // 1 minute
    this.timeout = options.timeout || 30000; // 30 seconds
    this.monitoringPeriod = options.monitoringPeriod || 60000; // 1 minute
    this.halfOpenMaxCalls = options.halfOpenMaxCalls || 3;
    
    // State tracking
    this.failureCount = 0;
    this.successCount = 0;
    this.lastFailureTime = null;
    this.nextAttemptTime = null;
    this.halfOpenCallCount = 0;
    
    // Statistics
    this.stats = {
      totalCalls: 0,
      totalFailures: 0,
      totalSuccesses: 0,
      totalTimeouts: 0,
      totalCircuitOpenBlocks: 0,
      averageResponseTime: 0,
      lastResetTime: Date.now()
    };
    
    logger.info(`Circuit breaker initialized: ${name}`, {
      failureThreshold: this.failureThreshold,
      recoveryTimeout: this.recoveryTimeout,
      timeout: this.timeout
    });
  }

  /**
   * Execute a function with circuit breaker protection
   * @param {Function} fn - Function to execute
   * @param {*} fallback - Fallback value/function if circuit is open
   * @returns {Promise} - Result of function execution or fallback
   */
  async execute(fn, fallback = null) {
    this.stats.totalCalls++;
    
    // Check if circuit is open
    if (this.state === STATES.OPEN) {
      if (Date.now() < this.nextAttemptTime) {
        this.stats.totalCircuitOpenBlocks++;
        logger.debug(`Circuit breaker ${this.name} is OPEN, blocking call`, {
          nextAttemptTime: this.nextAttemptTime,
          timeRemaining: this.nextAttemptTime - Date.now()
        });
        
        return this.handleFallback(fallback, 'CIRCUIT_OPEN');
      } else {
        // Transition to half-open
        this.transitionToHalfOpen();
      }
    }
    
    // Check if we're in half-open and have exceeded max calls
    if (this.state === STATES.HALF_OPEN && this.halfOpenCallCount >= this.halfOpenMaxCalls) {
      logger.debug(`Circuit breaker ${this.name} half-open max calls exceeded`, {
        halfOpenCallCount: this.halfOpenCallCount,
        maxCalls: this.halfOpenMaxCalls
      });
      return this.handleFallback(fallback, 'HALF_OPEN_LIMIT');
    }
    
    const startTime = Date.now();
    
    try {
      // Execute with timeout
      const result = await this.executeWithTimeout(fn);
      
      // Record success
      this.onSuccess(Date.now() - startTime);
      
      return result;
    } catch (error) {
      // Record failure
      this.onFailure(error, Date.now() - startTime);
      
      // If circuit is now open, return fallback
      if (this.state === STATES.OPEN) {
        return this.handleFallback(fallback, 'CIRCUIT_OPENED');
      }
      
      // Re-throw error if circuit is still closed/half-open
      throw error;
    }
  }

  /**
   * Execute function with timeout
   * @param {Function} fn - Function to execute
   * @returns {Promise} - Result of function execution
   */
  async executeWithTimeout(fn) {
    return new Promise(async (resolve, reject) => {
      const timeoutId = setTimeout(() => {
        this.stats.totalTimeouts++;
        reject(new Error(`Circuit breaker timeout: ${this.name} (${this.timeout}ms)`));
      }, this.timeout);
      
      try {
        const result = await fn();
        clearTimeout(timeoutId);
        resolve(result);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  /**
   * Handle successful execution
   * @param {number} responseTime - Response time in milliseconds
   */
  onSuccess(responseTime) {
    this.stats.totalSuccesses++;
    this.stats.averageResponseTime = this.updateAverageResponseTime(responseTime);
    
    if (this.state === STATES.HALF_OPEN) {
      this.halfOpenCallCount++;
      this.successCount++;
      
      // If we've had enough successful calls, close the circuit
      if (this.successCount >= this.halfOpenMaxCalls) {
        this.transitionToClosed();
      }
    } else {
      this.successCount++;
      this.failureCount = 0; // Reset failure count on success
    }
    
    logger.debug(`Circuit breaker ${this.name} recorded success`, {
      state: this.state,
      successCount: this.successCount,
      responseTime
    });
  }

  /**
   * Handle failed execution
   * @param {Error} error - Error that occurred
   * @param {number} responseTime - Response time in milliseconds
   */
  onFailure(error, responseTime) {
    this.stats.totalFailures++;
    this.stats.averageResponseTime = this.updateAverageResponseTime(responseTime);
    
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    if (this.state === STATES.HALF_OPEN) {
      // Any failure in half-open state opens the circuit
      this.transitionToOpen();
    } else if (this.failureCount >= this.failureThreshold) {
      // Too many failures, open the circuit
      this.transitionToOpen();
    }
    
    logger.warn(`Circuit breaker ${this.name} recorded failure`, {
      state: this.state,
      failureCount: this.failureCount,
      threshold: this.failureThreshold,
      error: error.message,
      responseTime
    });
  }

  /**
   * Transition to CLOSED state
   */
  transitionToClosed() {
    this.state = STATES.CLOSED;
    this.failureCount = 0;
    this.successCount = 0;
    this.halfOpenCallCount = 0;
    this.nextAttemptTime = null;
    
    logger.info(`Circuit breaker ${this.name} transitioned to CLOSED`);
  }

  /**
   * Transition to OPEN state
   */
  transitionToOpen() {
    this.state = STATES.OPEN;
    this.nextAttemptTime = Date.now() + this.recoveryTimeout;
    this.halfOpenCallCount = 0;
    
    logger.warn(`Circuit breaker ${this.name} transitioned to OPEN`, {
      nextAttemptTime: this.nextAttemptTime,
      recoveryTimeout: this.recoveryTimeout
    });
  }

  /**
   * Transition to HALF_OPEN state
   */
  transitionToHalfOpen() {
    this.state = STATES.HALF_OPEN;
    this.halfOpenCallCount = 0;
    this.successCount = 0;
    
    logger.info(`Circuit breaker ${this.name} transitioned to HALF_OPEN`);
  }

  /**
   * Handle fallback when circuit is open
   * @param {*} fallback - Fallback value or function
   * @param {string} reason - Reason for fallback
   * @returns {*} - Fallback result
   */
  async handleFallback(fallback, reason) {
    if (typeof fallback === 'function') {
      try {
        return await fallback();
      } catch (error) {
        logger.error(`Circuit breaker ${this.name} fallback function failed`, {
          reason,
          error: error.message
        });
        throw new Error(`Circuit breaker ${this.name} open and fallback failed: ${error.message}`);
      }
    } else if (fallback !== null && fallback !== undefined) {
      return fallback;
    } else {
      throw new Error(`Circuit breaker ${this.name} is open (${reason})`);
    }
  }

  /**
   * Update average response time
   * @param {number} responseTime - New response time
   * @returns {number} - Updated average
   */
  updateAverageResponseTime(responseTime) {
    const totalCalls = this.stats.totalSuccesses + this.stats.totalFailures;
    if (totalCalls === 1) {
      return responseTime;
    }
    
    return ((this.stats.averageResponseTime * (totalCalls - 1)) + responseTime) / totalCalls;
  }

  /**
   * Get current circuit breaker status
   * @returns {Object} - Status information
   */
  getStatus() {
    return {
      name: this.name,
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      failureThreshold: this.failureThreshold,
      nextAttemptTime: this.nextAttemptTime,
      halfOpenCallCount: this.halfOpenCallCount,
      stats: { ...this.stats }
    };
  }

  /**
   * Reset circuit breaker to initial state
   */
  reset() {
    this.transitionToClosed();
    this.stats = {
      totalCalls: 0,
      totalFailures: 0,
      totalSuccesses: 0,
      totalTimeouts: 0,
      totalCircuitOpenBlocks: 0,
      averageResponseTime: 0,
      lastResetTime: Date.now()
    };
    
    logger.info(`Circuit breaker ${this.name} reset`);
  }
}

// Circuit breaker registry
const circuitBreakers = new Map();

/**
 * Get or create a circuit breaker
 * @param {string} name - Circuit breaker name
 * @param {Object} options - Configuration options
 * @returns {CircuitBreaker} - Circuit breaker instance
 */
const getCircuitBreaker = (name, options = {}) => {
  if (!circuitBreakers.has(name)) {
    circuitBreakers.set(name, new CircuitBreaker(name, options));
  }
  return circuitBreakers.get(name);
};

/**
 * Get all circuit breaker statuses
 * @returns {Array} - Array of circuit breaker statuses
 */
const getAllStatuses = () => {
  return Array.from(circuitBreakers.values()).map(cb => cb.getStatus());
};

module.exports = {
  CircuitBreaker,
  getCircuitBreaker,
  getAllStatuses,
  STATES
};
