/**
 * Notification Deduplication Service
 * Prevents duplicate notifications from being sent to users
 */

const crypto = require('crypto');
const logger = require('../utils/logger');

class NotificationDeduplicationService {
  constructor() {
    // In-memory store for notification hashes
    // In production, this should be replaced with Redis or similar
    this.sentNotifications = new Map();
    this.cleanupInterval = 60 * 60 * 1000; // 1 hour
    this.notificationTTL = 24 * 60 * 60 * 1000; // 24 hours
    
    // Start cleanup interval
    this.startCleanup();
  }

  /**
   * Generate notification hash for deduplication
   * @param {String} userId - User ID
   * @param {String} type - Notification type
   * @param {Object} data - Notification data
   * @returns {String} - Notification hash
   */
  generateNotificationHash(userId, type, data) {
    // Create a normalized data object for consistent hashing
    const normalizedData = this.normalizeNotificationData(type, data);
    
    const hashData = {
      userId,
      type,
      data: normalizedData
    };
    
    return crypto.createHash('sha256').update(JSON.stringify(hashData)).digest('hex');
  }

  /**
   * Normalize notification data for consistent hashing
   * @param {String} type - Notification type
   * @param {Object} data - Notification data
   * @returns {Object} - Normalized data
   */
  normalizeNotificationData(type, data) {
    const normalized = {};
    
    switch (type) {
      case 'assessment_completed':
      case 'assessment_failed':
        // For assessment notifications, only include jobId and status
        normalized.jobId = data.jobId;
        normalized.status = data.status;
        break;
        
      case 'analysis_result_ready':
        // For result notifications, include jobId and resultId
        normalized.jobId = data.jobId;
        normalized.resultId = data.resultId;
        break;
        
      case 'system_notification':
        // For system notifications, include message and priority
        normalized.message = data.message;
        normalized.priority = data.priority || 'normal';
        break;
        
      default:
        // For other types, include all data but sort keys
        normalized = this.sortObjectKeys(data);
    }
    
    return normalized;
  }

  /**
   * Sort object keys for consistent hashing
   * @param {Object} obj - Object to sort
   * @returns {Object} - Object with sorted keys
   */
  sortObjectKeys(obj) {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }
    
    const sorted = {};
    Object.keys(obj).sort().forEach(key => {
      sorted[key] = typeof obj[key] === 'object' ? this.sortObjectKeys(obj[key]) : obj[key];
    });
    return sorted;
  }

  /**
   * Check if notification is duplicate
   * @param {String} userId - User ID
   * @param {String} type - Notification type
   * @param {Object} data - Notification data
   * @returns {Object} - Deduplication result
   */
  checkDuplicate(userId, type, data) {
    const notificationHash = this.generateNotificationHash(userId, type, data);
    const existing = this.sentNotifications.get(notificationHash);
    
    if (existing) {
      const timeSinceLastSent = Date.now() - existing.timestamp;
      
      logger.debug('Duplicate notification detected', {
        userId,
        type,
        notificationHash,
        timeSinceLastSent,
        originalTimestamp: existing.timestamp
      });
      
      return {
        isDuplicate: true,
        notificationHash,
        originalTimestamp: existing.timestamp,
        timeSinceLastSent
      };
    }
    
    return {
      isDuplicate: false,
      notificationHash
    };
  }

  /**
   * Mark notification as sent
   * @param {String} userId - User ID
   * @param {String} type - Notification type
   * @param {Object} data - Notification data
   * @param {String} notificationHash - Pre-computed hash (optional)
   */
  markAsSent(userId, type, data, notificationHash = null) {
    const hash = notificationHash || this.generateNotificationHash(userId, type, data);
    
    this.sentNotifications.set(hash, {
      userId,
      type,
      timestamp: Date.now(),
      data: this.normalizeNotificationData(type, data)
    });
    
    logger.debug('Notification marked as sent', {
      userId,
      type,
      notificationHash: hash
    });
  }

  /**
   * Check if notification should be sent based on deduplication rules
   * @param {String} userId - User ID
   * @param {String} type - Notification type
   * @param {Object} data - Notification data
   * @param {Object} options - Deduplication options
   * @returns {Object} - Decision result
   */
  shouldSendNotification(userId, type, data, options = {}) {
    const {
      allowDuplicateAfter = this.getDefaultDuplicateThreshold(type),
      forceOverride = false
    } = options;
    
    if (forceOverride) {
      logger.debug('Notification force override enabled', { userId, type });
      return {
        shouldSend: true,
        reason: 'FORCE_OVERRIDE'
      };
    }
    
    const deduplicationResult = this.checkDuplicate(userId, type, data);
    
    if (!deduplicationResult.isDuplicate) {
      return {
        shouldSend: true,
        reason: 'NEW_NOTIFICATION',
        notificationHash: deduplicationResult.notificationHash
      };
    }
    
    // Check if enough time has passed to allow duplicate
    if (deduplicationResult.timeSinceLastSent >= allowDuplicateAfter) {
      logger.info('Allowing duplicate notification after threshold', {
        userId,
        type,
        timeSinceLastSent: deduplicationResult.timeSinceLastSent,
        threshold: allowDuplicateAfter
      });
      
      return {
        shouldSend: true,
        reason: 'THRESHOLD_EXCEEDED',
        notificationHash: deduplicationResult.notificationHash
      };
    }
    
    return {
      shouldSend: false,
      reason: 'DUPLICATE_DETECTED',
      timeSinceLastSent: deduplicationResult.timeSinceLastSent,
      threshold: allowDuplicateAfter
    };
  }

  /**
   * Get default duplicate threshold for notification type
   * @param {String} type - Notification type
   * @returns {Number} - Threshold in milliseconds
   */
  getDefaultDuplicateThreshold(type) {
    const thresholds = {
      'assessment_completed': 5 * 60 * 1000, // 5 minutes
      'assessment_failed': 2 * 60 * 1000,    // 2 minutes
      'analysis_result_ready': 10 * 60 * 1000, // 10 minutes
      'system_notification': 30 * 60 * 1000,   // 30 minutes
      'default': 5 * 60 * 1000 // 5 minutes
    };
    
    return thresholds[type] || thresholds.default;
  }

  /**
   * Start cleanup interval to remove old notifications
   */
  startCleanup() {
    setInterval(() => {
      this.cleanup();
    }, this.cleanupInterval);
    
    logger.info('Notification deduplication cleanup started', {
      interval: this.cleanupInterval,
      ttl: this.notificationTTL
    });
  }

  /**
   * Clean up old notifications
   */
  cleanup() {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [hash, notification] of this.sentNotifications.entries()) {
      if (now - notification.timestamp > this.notificationTTL) {
        this.sentNotifications.delete(hash);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      logger.debug('Cleaned up old notifications', {
        cleanedCount,
        remainingCount: this.sentNotifications.size
      });
    }
  }

  /**
   * Get statistics about the deduplication service
   * @returns {Object} - Statistics
   */
  getStats() {
    return {
      totalTrackedNotifications: this.sentNotifications.size,
      cleanupInterval: this.cleanupInterval,
      notificationTTL: this.notificationTTL
    };
  }

  /**
   * Clear all tracked notifications (for testing)
   */
  clear() {
    this.sentNotifications.clear();
    logger.debug('Notification deduplication cache cleared');
  }
}

// Create singleton instance
const notificationDeduplicationService = new NotificationDeduplicationService();

module.exports = notificationDeduplicationService;
