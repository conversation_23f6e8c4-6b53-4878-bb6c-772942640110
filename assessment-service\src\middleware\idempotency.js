/**
 * Idempotency Middleware for Assessment Service
 * Prevents duplicate assessment submissions by checking for existing assessments
 * and generating assessment_id if not provided by client
 */

const { v4: uuidv4 } = require('uuid');
const archiveService = require('../services/archiveService');
const logger = require('../utils/logger');
const { sendSuccess } = require('../utils/responseHelper');

/**
 * Check assessment idempotency middleware
 * Checks if assessment with same assessment_id already exists for the user
 * If exists, returns the existing result
 * If not provided, generates a new assessment_id
 */
const checkAssessmentIdempotency = async (req, res, next) => {
  try {
    const { assessment_id } = req.body;
    const { id: userId } = req.user;
    
    if (assessment_id) {
      logger.info('Checking idempotency for assessment', {
        assessmentId: assessment_id,
        userId
      });
      
      // Check if assessment already exists
      try {
        const existingJob = await archiveService.getJobByAssessmentId(assessment_id, userId);
        
        if (existingJob) {
          logger.info('Idempotent request detected - returning existing assessment', {
            assessmentId: assessment_id,
            userId,
            existingJobId: existingJob.job_id,
            status: existingJob.status
          });
          
          // Return existing assessment result
          return sendSuccess(res, 'Assessment already submitted', {
            jobId: existingJob.job_id,
            assessmentId: assessment_id,
            status: existingJob.status,
            idempotent: true,
            created_at: existingJob.created_at,
            message: 'This assessment was already submitted and is being processed'
          }, 200);
        }
        
        logger.debug('No existing assessment found, proceeding with submission', {
          assessmentId: assessment_id,
          userId
        });
        
      } catch (error) {
        // If error is 404 (not found), continue with normal processing
        if (error.response?.status === 404) {
          logger.debug('Assessment not found in archive, proceeding with new submission', {
            assessmentId: assessment_id,
            userId
          });
        } else {
          // For other errors, log but continue (don't block submission)
          logger.warn('Error checking existing assessment, continuing with submission', {
            assessmentId: assessment_id,
            userId,
            error: error.message
          });
        }
      }
    } else {
      // Generate assessment_id if not provided
      req.body.assessment_id = uuidv4();
      
      logger.info('Generated assessment_id for submission', {
        assessmentId: req.body.assessment_id,
        userId
      });
    }
    
    // Continue to next middleware
    next();
    
  } catch (error) {
    logger.error('Error in idempotency middleware', {
      userId: req.user?.id,
      error: error.message
    });
    
    // Don't block the request on idempotency check errors
    // Generate new assessment_id and continue
    if (!req.body.assessment_id) {
      req.body.assessment_id = uuidv4();
    }
    
    next();
  }
};

/**
 * Validate assessment_id format if provided
 */
const validateAssessmentId = (req, res, next) => {
  const { assessment_id } = req.body;
  
  if (assessment_id) {
    // UUID v4 regex pattern
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    
    if (!uuidRegex.test(assessment_id)) {
      logger.warn('Invalid assessment_id format provided', {
        assessmentId: assessment_id,
        userId: req.user?.id
      });
      
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_ASSESSMENT_ID',
          message: 'Assessment ID must be a valid UUID v4 format',
          details: {
            assessment_id: 'Must be a valid UUID v4 format (e.g., 123e4567-e89b-12d3-a456-************)'
          }
        }
      });
    }
  }
  
  next();
};

module.exports = {
  checkAssessmentIdempotency,
  validateAssessmentId
};
