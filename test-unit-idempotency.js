/**
 * Unit Testing untuk Idempotency Components
 * Tests individual components tanpa dependency pada external services
 */

const { v4: uuidv4 } = require('uuid');

// Mock implementations untuk testing
class MockNotificationDeduplicationService {
  constructor() {
    this.sentNotifications = new Map();
    this.notificationTTL = 5000; // 5 seconds untuk testing
  }

  generateNotificationHash(userId, type, data) {
    const hashData = { userId, type, data: this.normalizeNotificationData(type, data) };
    return JSON.stringify(hashData);
  }

  normalizeNotificationData(type, data) {
    switch (type) {
      case 'assessment_completed':
        return { jobId: data.jobId, status: data.status };
      case 'assessment_failed':
        return { jobId: data.jobId, error: data.error };
      default:
        return data;
    }
  }

  checkDuplicate(userId, type, data) {
    const hash = this.generateNotificationHash(userId, type, data);
    const existing = this.sentNotifications.get(hash);
    
    if (existing) {
      const timeSinceLastSent = Date.now() - existing.timestamp;
      return {
        isDuplicate: true,
        notificationHash: hash,
        timeSinceLastSent
      };
    }
    
    return { isDuplicate: false, notificationHash: hash };
  }

  shouldSendNotification(userId, type, data, options = {}) {
    const { allowDuplicateAfter = 5000, forceOverride = false } = options;
    
    if (forceOverride) {
      return { shouldSend: true, reason: 'FORCE_OVERRIDE' };
    }
    
    const deduplicationResult = this.checkDuplicate(userId, type, data);
    
    if (!deduplicationResult.isDuplicate) {
      return { shouldSend: true, reason: 'NEW_NOTIFICATION', notificationHash: deduplicationResult.notificationHash };
    }
    
    if (deduplicationResult.timeSinceLastSent >= allowDuplicateAfter) {
      return { shouldSend: true, reason: 'THRESHOLD_EXCEEDED', notificationHash: deduplicationResult.notificationHash };
    }
    
    return { shouldSend: false, reason: 'DUPLICATE_DETECTED', timeSinceLastSent: deduplicationResult.timeSinceLastSent };
  }

  markAsSent(userId, type, data) {
    const hash = this.generateNotificationHash(userId, type, data);
    this.sentNotifications.set(hash, { userId, type, timestamp: Date.now() });
  }

  clear() {
    this.sentNotifications.clear();
  }
}

// Mock Circuit Breaker
class MockCircuitBreaker {
  constructor(name, options = {}) {
    this.name = name;
    this.state = 'CLOSED';
    this.failureCount = 0;
    this.failureThreshold = options.failureThreshold || 3;
    this.stats = { totalCalls: 0, totalFailures: 0, totalSuccesses: 0 };
  }

  async execute(fn, fallback = null) {
    this.stats.totalCalls++;
    
    if (this.state === 'OPEN') {
      if (fallback) {
        return typeof fallback === 'function' ? await fallback() : fallback;
      }
      throw new Error(`Circuit breaker ${this.name} is open`);
    }
    
    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  onSuccess() {
    this.stats.totalSuccesses++;
    this.failureCount = 0;
    if (this.state === 'HALF_OPEN') {
      this.state = 'CLOSED';
    }
  }

  onFailure() {
    this.stats.totalFailures++;
    this.failureCount++;
    if (this.failureCount >= this.failureThreshold) {
      this.state = 'OPEN';
    }
  }

  getStatus() {
    return {
      name: this.name,
      state: this.state,
      failureCount: this.failureCount,
      stats: { ...this.stats }
    };
  }

  reset() {
    this.state = 'CLOSED';
    this.failureCount = 0;
    this.stats = { totalCalls: 0, totalFailures: 0, totalSuccesses: 0 };
  }
}

// Test results tracking
const testResults = { passed: 0, failed: 0, total: 0, details: [] };

function runTest(testName, testFn) {
  testResults.total++;
  try {
    const result = testFn();
    if (result) {
      console.log(`✅ ${testName}`);
      testResults.passed++;
      testResults.details.push({ testName, status: true });
    } else {
      console.log(`❌ ${testName}`);
      testResults.failed++;
      testResults.details.push({ testName, status: false });
    }
  } catch (error) {
    console.log(`❌ ${testName} - Error: ${error.message}`);
    testResults.failed++;
    testResults.details.push({ testName, status: false, error: error.message });
  }
}

async function runAsyncTest(testName, testFn) {
  testResults.total++;
  try {
    const result = await testFn();
    if (result) {
      console.log(`✅ ${testName}`);
      testResults.passed++;
      testResults.details.push({ testName, status: true });
    } else {
      console.log(`❌ ${testName}`);
      testResults.failed++;
      testResults.details.push({ testName, status: false });
    }
  } catch (error) {
    console.log(`❌ ${testName} - Error: ${error.message}`);
    testResults.failed++;
    testResults.details.push({ testName, status: false, error: error.message });
  }
}

/**
 * Test Suite 1: Notification Deduplication Service
 */
function testNotificationDeduplication() {
  console.log('\n🧪 Test Suite 1: Notification Deduplication Service');
  console.log('='.repeat(60));
  
  const service = new MockNotificationDeduplicationService();
  const userId = uuidv4();
  const jobId = uuidv4();
  
  // Test 1.1: Hash generation consistency
  runTest('1.1 Hash generation consistency', () => {
    const data = { jobId, status: 'completed' };
    const hash1 = service.generateNotificationHash(userId, 'assessment_completed', data);
    const hash2 = service.generateNotificationHash(userId, 'assessment_completed', data);
    return hash1 === hash2;
  });
  
  // Test 1.2: Different data produces different hash
  runTest('1.2 Different data produces different hash', () => {
    const data1 = { jobId: uuidv4(), status: 'completed' };
    const data2 = { jobId: uuidv4(), status: 'completed' };
    const hash1 = service.generateNotificationHash(userId, 'assessment_completed', data1);
    const hash2 = service.generateNotificationHash(userId, 'assessment_completed', data2);
    return hash1 !== hash2;
  });
  
  // Test 1.3: New notification should be sent
  runTest('1.3 New notification should be sent', () => {
    const data = { jobId, status: 'completed' };
    const decision = service.shouldSendNotification(userId, 'assessment_completed', data);
    return decision.shouldSend === true && decision.reason === 'NEW_NOTIFICATION';
  });
  
  // Test 1.4: Duplicate notification should be blocked
  runTest('1.4 Duplicate notification should be blocked', () => {
    const data = { jobId, status: 'completed' };
    service.markAsSent(userId, 'assessment_completed', data);
    const decision = service.shouldSendNotification(userId, 'assessment_completed', data);
    return decision.shouldSend === false && decision.reason === 'DUPLICATE_DETECTED';
  });
  
  // Test 1.5: Force override should work
  runTest('1.5 Force override should work', () => {
    const data = { jobId, status: 'completed' };
    const decision = service.shouldSendNotification(userId, 'assessment_completed', data, { forceOverride: true });
    return decision.shouldSend === true && decision.reason === 'FORCE_OVERRIDE';
  });
  
  // Test 1.6: Different notification types should not interfere
  runTest('1.6 Different notification types should not interfere', () => {
    service.clear();
    const data = { jobId, status: 'completed' };
    service.markAsSent(userId, 'assessment_completed', data);
    const decision = service.shouldSendNotification(userId, 'assessment_failed', data);
    return decision.shouldSend === true;
  });
}

/**
 * Test Suite 2: Circuit Breaker
 */
async function testCircuitBreaker() {
  console.log('\n🧪 Test Suite 2: Circuit Breaker');
  console.log('='.repeat(40));
  
  const circuitBreaker = new MockCircuitBreaker('test-service', { failureThreshold: 3 });
  
  // Test 2.1: Successful operations keep circuit closed
  await runAsyncTest('2.1 Successful operations keep circuit closed', async () => {
    await circuitBreaker.execute(() => Promise.resolve('success'));
    await circuitBreaker.execute(() => Promise.resolve('success'));
    return circuitBreaker.getStatus().state === 'CLOSED';
  });
  
  // Test 2.2: Multiple failures open circuit
  await runAsyncTest('2.2 Multiple failures open circuit', async () => {
    circuitBreaker.reset();
    
    for (let i = 0; i < 3; i++) {
      try {
        await circuitBreaker.execute(() => Promise.reject(new Error('test failure')));
      } catch (e) {
        // Expected to fail
      }
    }
    
    return circuitBreaker.getStatus().state === 'OPEN';
  });
  
  // Test 2.3: Open circuit blocks calls
  await runAsyncTest('2.3 Open circuit blocks calls', async () => {
    try {
      await circuitBreaker.execute(() => Promise.resolve('should not execute'));
      return false; // Should not reach here
    } catch (error) {
      return error.message.includes('Circuit breaker') && error.message.includes('is open');
    }
  });
  
  // Test 2.4: Fallback works when circuit is open
  await runAsyncTest('2.4 Fallback works when circuit is open', async () => {
    const result = await circuitBreaker.execute(
      () => Promise.resolve('should not execute'),
      () => 'fallback result'
    );
    return result === 'fallback result';
  });
  
  // Test 2.5: Statistics tracking
  await runAsyncTest('2.5 Statistics tracking', async () => {
    const stats = circuitBreaker.getStatus().stats;
    return stats.totalCalls > 0 && stats.totalFailures > 0;
  });
}

/**
 * Test Suite 3: Assessment ID Validation
 */
function testAssessmentIdValidation() {
  console.log('\n🧪 Test Suite 3: Assessment ID Validation');
  console.log('='.repeat(45));
  
  // Mock validation function
  function validateAssessmentId(assessmentId) {
    if (!assessmentId) return { valid: true, generated: true, id: uuidv4() };
    
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(assessmentId)) {
      return { valid: false, error: 'Invalid UUID format' };
    }
    
    return { valid: true, generated: false, id: assessmentId };
  }
  
  // Test 3.1: Valid UUID passes validation
  runTest('3.1 Valid UUID passes validation', () => {
    const validUuid = uuidv4();
    const result = validateAssessmentId(validUuid);
    return result.valid === true && result.id === validUuid;
  });
  
  // Test 3.2: Invalid UUID fails validation
  runTest('3.2 Invalid UUID fails validation', () => {
    const result = validateAssessmentId('invalid-uuid');
    return result.valid === false && result.error;
  });
  
  // Test 3.3: Empty assessment_id generates new UUID
  runTest('3.3 Empty assessment_id generates new UUID', () => {
    const result = validateAssessmentId(null);
    return result.valid === true && result.generated === true && result.id;
  });
  
  // Test 3.4: Generated UUID is valid format
  runTest('3.4 Generated UUID is valid format', () => {
    const result = validateAssessmentId(null);
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(result.id);
  });
}

/**
 * Test Suite 4: Error Categorization
 */
function testErrorCategorization() {
  console.log('\n🧪 Test Suite 4: Error Categorization');
  console.log('='.repeat(42));
  
  // Mock error types
  const ERROR_TYPES = {
    NETWORK_ERROR: { code: 'NETWORK_ERROR', isRetryable: true },
    VALIDATION_ERROR: { code: 'VALIDATION_ERROR', isRetryable: false },
    CIRCUIT_BREAKER_OPEN: { code: 'CIRCUIT_BREAKER_OPEN', isRetryable: true },
    AUTH_ERROR: { code: 'AUTH_ERROR', isRetryable: false }
  };
  
  function shouldRetry(error) {
    return error.isRetryable === true;
  }
  
  // Test 4.1: Network errors are retryable
  runTest('4.1 Network errors are retryable', () => {
    return shouldRetry(ERROR_TYPES.NETWORK_ERROR) === true;
  });
  
  // Test 4.2: Validation errors are not retryable
  runTest('4.2 Validation errors are not retryable', () => {
    return shouldRetry(ERROR_TYPES.VALIDATION_ERROR) === false;
  });
  
  // Test 4.3: Circuit breaker errors are retryable
  runTest('4.3 Circuit breaker errors are retryable', () => {
    return shouldRetry(ERROR_TYPES.CIRCUIT_BREAKER_OPEN) === true;
  });
  
  // Test 4.4: Auth errors are not retryable
  runTest('4.4 Auth errors are not retryable', () => {
    return shouldRetry(ERROR_TYPES.AUTH_ERROR) === false;
  });
}

/**
 * Main test runner
 */
async function runUnitTests() {
  console.log('🚀 Starting Unit Tests for Idempotency Components');
  console.log('='.repeat(65));
  
  // Run all test suites
  testNotificationDeduplication();
  await testCircuitBreaker();
  testAssessmentIdValidation();
  testErrorCategorization();
  
  // Test summary
  console.log('\n📊 Unit Test Results Summary');
  console.log('='.repeat(35));
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed} ✅`);
  console.log(`Failed: ${testResults.failed} ❌`);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.details
      .filter(t => !t.status)
      .forEach(t => console.log(`   - ${t.testName}${t.error ? ': ' + t.error : ''}`));
  }
  
  console.log('\n🎯 Overall Result:', testResults.failed === 0 ? '✅ ALL UNIT TESTS PASSED' : '⚠️ SOME UNIT TESTS FAILED');
  
  return testResults.failed === 0;
}

// Run unit tests
runUnitTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(console.error);
