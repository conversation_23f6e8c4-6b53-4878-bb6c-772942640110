const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');
const notificationDeduplicationService = require('./notificationDeduplicationService');

class SocketService {
  constructor() {
    this.io = null;
    this.userConnections = new Map(); // userId -> Set of socket IDs
  }

  initialize(io) {
    this.io = io;
    this.setupSocketHandlers();
    logger.info('Socket service initialized');
  }

  setupSocketHandlers() {
    this.io.on('connection', (socket) => {
      logger.info(`Socket connected: ${socket.id}`, {
        service: 'notification-service',
        socketId: socket.id,
        remoteAddress: socket.handshake.address,
        userAgent: socket.handshake.headers['user-agent']
      });

      // Handle authentication
      socket.on('authenticate', (data) => {
        this.authenticateSocket(socket, data.token);
      });

      // Handle disconnection
      socket.on('disconnect', (reason) => {
        this.handleDisconnect(socket, reason);
      });

      // Handle reconnection
      socket.on('reconnecting', (attemptNumber) => {
        logger.info(`Socket reconnecting: ${socket.id}`, {
          socketId: socket.id,
          attemptNumber,
          userId: socket.userId
        });
      });

      // Set timeout for authentication
      setTimeout(() => {
        if (!socket.userId) {
          logger.warn(`Socket ${socket.id} not authenticated within timeout`);
          socket.emit('auth_error', { message: 'Authentication timeout' });
          socket.disconnect();
        }
      }, 10000); // 10 seconds timeout
    });
  }

  authenticateSocket(socket, token) {
    try {
      if (!token) {
        socket.emit('auth_error', { message: 'Token required' });
        return;
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      socket.userId = decoded.id;
      socket.userEmail = decoded.email;

      // Add to user connections map
      if (!this.userConnections.has(decoded.id)) {
        this.userConnections.set(decoded.id, new Set());
      }
      this.userConnections.get(decoded.id).add(socket.id);

      // Join user-specific room
      socket.join(`user:${decoded.id}`);

      socket.emit('authenticated', {
        success: true,
        userId: decoded.id,
        email: decoded.email
      });

      logger.info(`Socket authenticated for user ${decoded.email}`, {
        socketId: socket.id,
        userId: decoded.id
      });

    } catch (error) {
      logger.warn(`Socket authentication failed: ${error.message}`, {
        socketId: socket.id
      });
      
      socket.emit('auth_error', { 
        message: error.name === 'TokenExpiredError' ? 'Token expired' : 'Invalid token' 
      });
      socket.disconnect();
    }
  }

  handleDisconnect(socket, reason = 'unknown') {
    if (socket.userId) {
      const userSockets = this.userConnections.get(socket.userId);
      if (userSockets) {
        userSockets.delete(socket.id);
        if (userSockets.size === 0) {
          this.userConnections.delete(socket.userId);
        }
      }

      logger.info(`Socket disconnected for user ${socket.userEmail}`, {
        service: 'notification-service',
        socketId: socket.id,
        userId: socket.userId,
        reason: reason,
        remainingConnections: userSockets ? userSockets.size : 0
      });
    } else {
      logger.info(`Unauthenticated socket disconnected: ${socket.id}`, {
        service: 'notification-service',
        socketId: socket.id,
        reason: reason
      });
    }
  }

  // Send notification to specific user with deduplication
  sendToUser(userId, event, data, options = {}) {
    const {
      skipDeduplication = false,
      allowDuplicateAfter = null,
      forceOverride = false
    } = options;

    // Check for deduplication unless explicitly skipped
    if (!skipDeduplication) {
      const deduplicationOptions = {
        allowDuplicateAfter,
        forceOverride
      };

      const decision = notificationDeduplicationService.shouldSendNotification(
        userId,
        event,
        data,
        deduplicationOptions
      );

      if (!decision.shouldSend) {
        logger.debug('Notification blocked by deduplication', {
          userId,
          event,
          reason: decision.reason,
          timeSinceLastSent: decision.timeSinceLastSent,
          threshold: decision.threshold
        });
        return false;
      }

      logger.debug('Notification approved by deduplication', {
        userId,
        event,
        reason: decision.reason,
        notificationHash: decision.notificationHash
      });
    }

    const room = `user:${userId}`;
    const socketCount = this.io.sockets.adapter.rooms.get(room)?.size || 0;

    if (socketCount > 0) {
      const notificationData = {
        ...data,
        timestamp: new Date().toISOString()
      };

      this.io.to(room).emit(event, notificationData);

      // Mark notification as sent for deduplication tracking
      if (!skipDeduplication) {
        notificationDeduplicationService.markAsSent(userId, event, data);
      }

      logger.info(`Notification sent to user ${userId}`, {
        event,
        socketCount,
        data: data,
        deduplicationSkipped: skipDeduplication
      });

      return true;
    } else {
      logger.warn(`No active connections for user ${userId}`, { event });
      return false;
    }
  }

  // Get connection statistics
  getConnectionCount() {
    return {
      total: this.io.sockets.sockets.size,
      authenticated: this.userConnections.size,
      users: Array.from(this.userConnections.keys()).length
    };
  }

  // Get detailed connection info for debugging
  getConnectionDetails() {
    const connections = [];
    this.userConnections.forEach((socketIds, userId) => {
      socketIds.forEach(socketId => {
        const socket = this.io.sockets.sockets.get(socketId);
        if (socket) {
          connections.push({
            userId,
            socketId,
            userEmail: socket.userEmail,
            connected: socket.connected,
            connectedAt: socket.handshake.time,
            remoteAddress: socket.handshake.address
          });
        }
      });
    });
    return connections;
  }

  // Force disconnect user (for debugging)
  disconnectUser(userId) {
    const userSockets = this.userConnections.get(userId);
    if (userSockets) {
      userSockets.forEach(socketId => {
        const socket = this.io.sockets.sockets.get(socketId);
        if (socket) {
          socket.disconnect(true);
        }
      });
      logger.info(`Force disconnected all sockets for user ${userId}`);
      return true;
    }
    return false;
  }

  // Broadcast to all connected users
  broadcast(event, data) {
    this.io.emit(event, {
      ...data,
      timestamp: new Date().toISOString()
    });

    logger.info(`Broadcast sent to all users`, { event, data });
  }
}

module.exports = new SocketService();